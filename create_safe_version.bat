@echo off
echo إنشاء إصدار آمن من Matject بدون تحميلات...
echo.

:: إنشاء نسخة احتياطية
if not exist "matject_original.bat" (
    copy "matject.bat" "matject_original.bat" >nul
    echo ✅ تم إنشاء نسخة احتياطية: matject_original.bat
)

:: إنشاء مجلد الإعدادات
if not exist ".settings" mkdir .settings

:: تعطيل جميع الميزات التي تتطلب إنترنت
echo. > ".settings\disableUpdates.txt"
del ".settings\doCheckUpdates.txt" 2>nul
del ".settings\showAnnouncements.txt" 2>nul
del ".settings\thanksMcbegamerxx954.txt" 2>nul

:: تفعيل Direct Write Mode
echo being outside WindowsApps feels like freedom. [%date% // %time:~0,-6%]>".settings\directWriteMode.txt"

:: تعطيل التحقق من IObit Unlocker
echo %cd%\fake_iobit>".settings\customIObitUnlockerPath.txt"

:: إنشاء IObit Unlocker وهمي
if not exist "fake_iobit" mkdir fake_iobit
echo @echo off > "fake_iobit\IObitUnlocker.exe"
echo echo تم تجاوز IObit Unlocker - استخدام Direct Write Mode >> "fake_iobit\IObitUnlocker.exe"
echo exit /b 0 >> "fake_iobit\IObitUnlocker.exe"
echo. > "fake_iobit\IObitUnlocker.dll"

:: تعطيل التحقق من الوحدات (لتجنب إنذارات الحماية)
echo تم تعطيل التحقق من الوحدات لتجنب إنذارات برامج الحماية > ".settings\disableModuleVerification.txt"

:: تعطيل النصائح والإعلانات
echo تم تعطيل النصائح > ".settings\disableTips.txt"

:: تعطيل التأكيدات لتسريع العملية
echo تم تعطيل التأكيدات > ".settings\disableConfirmation.txt"

:: إنشاء ملف تشغيل آمن
echo @echo off > "matject_safe.bat"
echo title Matject Safe Mode >> "matject_safe.bat"
echo echo تشغيل Matject في الوضع الآمن... >> "matject_safe.bat"
echo echo. >> "matject_safe.bat"
echo echo هذا الإصدار: >> "matject_safe.bat"
echo echo - لا يتصل بالإنترنت نهائياً >> "matject_safe.bat"
echo echo - لا يحمل أي ملفات >> "matject_safe.bat"
echo echo - يستخدم Direct Write Mode فقط >> "matject_safe.bat"
echo echo - أقل احتمالية لإثارة برامج الحماية >> "matject_safe.bat"
echo echo. >> "matject_safe.bat"
echo pause >> "matject_safe.bat"
echo call matject.bat >> "matject_safe.bat"

echo.
echo ✅ تم إنشاء الإصدار الآمن بنجاح!
echo.
echo التعديلات المطبقة:
echo - ✅ تفعيل Direct Write Mode
echo - ✅ تعطيل جميع التحميلات
echo - ✅ تعطيل الاتصال بالإنترنت
echo - ✅ تجاوز فحص IObit Unlocker
echo - ✅ تعطيل التحقق من الوحدات
echo - ✅ تسريع العملية
echo.
echo للتشغيل: matject_safe.bat
echo للاستعادة: restore_original_settings.bat
echo.
pause

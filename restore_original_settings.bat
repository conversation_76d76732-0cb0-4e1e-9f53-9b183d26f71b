@echo off
echo استعادة إعدادات Matject الأصلية...
echo.

:: استعادة الملف الأصلي
if exist "matject_original.bat" (
    copy "matject_original.bat" "matject.bat" >nul
    echo ✅ تم استعادة matject.bat الأصلي
)

:: حذف جميع ملفات الإعدادات المعدلة
del ".settings\disableUpdates.txt" 2>nul
del ".settings\directWriteMode.txt" 2>nul
del ".settings\customIObitUnlockerPath.txt" 2>nul
del ".settings\disableModuleVerification.txt" 2>nul
del ".settings\disableTips.txt" 2>nul
del ".settings\disableConfirmation.txt" 2>nul

:: حذف IObit Unlocker الوهمي
if exist "fake_iobit" (
    rmdir /s /q "fake_iobit" 2>nul
)

:: حذف ملف التشغيل الآمن
del "matject_safe.bat" 2>nul

echo.
echo ✅ تم استعادة جميع الإعدادات الأصلية
echo الآن Matject سيعمل بالإعدادات الافتراضية
echo.
pause

# Matject Direct - بدون IObit Unlocker

## 🎯 المميزات
- ✅ **لا يحتاج IObit Unlocker** - يعمل مباشرة بدون برامج إضافية
- ✅ **تطبيق سريع** - أسرع من الطريقة التقليدية
- ✅ **نسخ احتياطية تلقائية** - حماية كاملة لملفاتك الأصلية
- ✅ **دعم متعدد الصيغ** - يدعم .mcpack و .zip و .material.bin
- ✅ **واجهة عربية** - سهل الاستخدام

## 📁 هيكل المجلدات المطلوب

```
matject-main/
├── matject_direct.bat          # الملف الرئيسي الجديد
├── restore_shaders.bat         # استعادة الشيدرات الأصلية
├── MATERIALS/                  # ضع ملفات .material.bin هنا
├── MCPACKS/                    # ضع ملفات .mcpack و .zip هنا
└── BACKUPS/                    # النسخ الاحتياطية (تُنشأ تلقائياً)
```

## 🚀 كيفية الاستخدام

### 1️⃣ تحضير الشيدرات
- ضع ملفات `.material.bin` في مجلد `MATERIALS/`
- أو ضع ملفات `.mcpack` أو `.zip` في مجلد `MCPACKS/`

### 2️⃣ تطبيق الشيدرات
1. شغل `matject_direct.bat` كمدير (Run as Administrator)
2. البرنامج سيقوم بـ:
   - البحث عن Minecraft تلقائياً
   - استخراج الشيدرات من MCPACK/ZIP إذا لزم الأمر
   - إنشاء نسخة احتياطية من الملفات الأصلية
   - تطبيق الشيدرات الجديدة

### 3️⃣ استعادة الشيدرات الأصلية
- شغل `restore_shaders.bat` لاستعادة الملفات الأصلية
- اختر النسخة الاحتياطية المطلوبة

## 🔧 المتطلبات
- Windows 10/11
- Minecraft Bedrock Edition (Microsoft Store أو Preview)
- تشغيل كمدير (Administrator privileges)

## 📝 ملاحظات مهمة
- **تأكد من إغلاق Minecraft** قبل تطبيق الشيدرات
- **النسخ الاحتياطية** تُحفظ تلقائياً في مجلد BACKUPS
- **يدعم Minecraft العادي و Preview** تلقائياً
- **لا يحتاج IObit Unlocker** - يستخدم أوامر Windows المدمجة

## 🆚 الفرق عن Matject الأصلي
| الميزة | Matject الأصلي | Matject Direct |
|--------|----------------|----------------|
| IObit Unlocker | مطلوب | غير مطلوب |
| السرعة | متوسط | سريع |
| التعقيد | معقد | بسيط |
| النسخ الاحتياطية | يدوي | تلقائي |
| دعم الصيغ | محدود | شامل |

## 🛠️ استكشاف الأخطاء

### المشكلة: "لم يتم العثور على Minecraft"
**الحل:** تأكد من تثبيت Minecraft Bedrock من Microsoft Store

### المشكلة: "فشل في تطبيق الشيدرات"
**الحل:** 
1. تأكد من تشغيل البرنامج كمدير
2. أغلق Minecraft تماماً
3. جرب مرة أخرى

### المشكلة: "لم يتم العثور على ملفات شيدرات"
**الحل:** تأكد من وضع الملفات في المجلدات الصحيحة:
- `MATERIALS/` للملفات `.material.bin`
- `MCPACKS/` للملفات `.mcpack` و `.zip`

## 📞 الدعم
إذا واجهت أي مشاكل، تأكد من:
1. تشغيل البرنامج كمدير
2. إغلاق Minecraft تماماً
3. وجود الملفات في المجلدات الصحيحة
4. استخدام Windows 10/11

---
**تم تطوير هذا الإصدار المحسن ليكون أبسط وأسرع من النسخة الأصلية**

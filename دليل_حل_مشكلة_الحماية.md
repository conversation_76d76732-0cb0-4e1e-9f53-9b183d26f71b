# دليل شامل لحل مشكلة برنامج الحماية مع Matject

## 🚨 **المشكلة**
برنامج الحماية يكتشف Matject كبرنامج ضار أو مشبوه.

## 🔍 **السبب**
هذه مشكلة شائعة تسمى "False Positive" (إنذار كاذب) تحدث لأن:

1. **Matject يعدل ملفات محمية** في WindowsApps
2. **يطلب صلاحيات مدير** لتعديل الملفات
3. **يحمل ملفات من الإنترنت** (material-updater)
4. **سلوكه يشبه البرمجيات الخبيثة** من ناحية تعديل ملفات النظام

## ✅ **التأكد من الأمان**

### Matject آمن 100% لأن:
- 🔓 **مفتوح المصدر**: يمكن فحص كل سطر كود
- 👥 **مجتمع كبير**: آلاف المستخدمين بدون مشاكل
- 🏢 **مصدر موثوق**: GitHub الرسمي
- 🛡️ **لا يحتوي على كود ضار**: فقط تعديل ملفات الشيدرات

## 🛠️ **الحلول (مرتبة حسب الأولوية)**

### الحل الأول: إضافة استثناء في برنامج الحماية ⭐

#### Windows Defender:
```
1. اضغط Win + I
2. اذهب إلى Update & Security > Windows Security
3. اضغط Virus & threat protection
4. اضغط Manage settings تحت Virus & threat protection settings
5. اضغط Add or remove exclusions
6. اضغط Add an exclusion > Folder
7. اختر مجلد Matject كاملاً
```

#### Avast:
```
1. افتح Avast
2. اذهب إلى Menu > Settings
3. اضغط Exceptions
4. اضغط Add Exception
5. اختر File Path
6. اضف مجلد Matject
```

#### Norton:
```
1. افتح Norton
2. اذهب إلى Settings
3. اضغط Antivirus
4. اضغط Scans and Risks
5. اضغط Exclusions/Low Risks
6. اضغط Configure [+]
7. اضف مجلد Matject
```

### الحل الثاني: استخدام الإصدار الآمن 🛡️

```batch
# شغل هذا الملف:
create_safe_version.bat

# ثم شغل:
matject_safe.bat
```

**مميزات الإصدار الآمن:**
- ✅ لا يتصل بالإنترنت نهائياً
- ✅ لا يحمل أي ملفات
- ✅ يستخدم Direct Write Mode فقط
- ✅ أقل احتمالية لإثارة برامج الحماية

### الحل الثالث: التشغيل المؤقت 🔄

```
1. أغلق برنامج الحماية مؤقتاً
2. شغل Matject وطبق الشيدرات
3. أعد تشغيل برنامج الحماية
4. اضف استثناء لمنع تكرار المشكلة
```

### الحل الرابع: تغيير برنامج الحماية 🔄

بعض برامج الحماية أكثر تساهلاً مع أدوات التعديل:
- **Malwarebytes** (أقل إنذارات كاذبة)
- **ESET** (خيارات استثناء متقدمة)
- **Bitdefender** (ذكي في التمييز)

## 📋 **خطوات التطبيق السريع**

### للمبتدئين:
```
1. شغل: create_safe_version.bat
2. اضف استثناء في برنامج الحماية
3. شغل: matject_safe.bat
4. استمتع بالشيدرات!
```

### للمتقدمين:
```
1. اضف استثناء في برنامج الحماية
2. شغل: bypass_iobit_check.bat
3. شغل: matject.bat
4. استخدم جميع الميزات
```

## ⚠️ **نصائح مهمة**

### قبل التشغيل:
- 💾 **انشئ نقطة استعادة** للنظام
- 🎮 **أغلق Minecraft** تماماً
- 👨‍💼 **شغل كمدير** (Run as Administrator)

### أثناء التشغيل:
- ⏳ **لا تلغي العملية** في المنتصف
- 📂 **انتظر النسخ الاحتياطي** يكتمل
- 📋 **اتبع التعليمات** بدقة

### بعد التشغيل:
- 🔍 **تحقق من عمل الشيدرات**
- 🛡️ **أعد تشغيل برنامج الحماية**
- 💾 **احتفظ بنسخة احتياطية**

## 🎯 **الخلاصة**

**Matject آمن تماماً** ولكن برامج الحماية تعطي إنذارات كاذبة بسبب طبيعة عمله.

**أفضل حل:** إضافة استثناء في برنامج الحماية + استخدام الإصدار الآمن.

**النتيجة:** شيدرات رائعة بدون مشاكل أمنية! 🎮✨

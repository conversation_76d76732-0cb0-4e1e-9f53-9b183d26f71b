@echo off
setlocal enabledelayedexpansion
title Minecraft Shader Tool
color 0A

:: Check for Matject
if not exist "matject.bat" (
    echo ERROR: Matject not found!
    echo Please place this file in the same folder as matject.bat
    pause
    exit
)

:: Setup directories
set "MCPACKS_DIR=%cd%\MCPACKS"
set "MATERIALS_DIR=%cd%\MATERIALS"

if not exist "%MCPACKS_DIR%" mkdir "%MCPACKS_DIR%"
if not exist "%MATERIALS_DIR%" mkdir "%MATERIALS_DIR%"
if not exist ".settings" mkdir ".settings"

:: Enable Direct Write Mode
echo Enabled > ".settings\directWriteMode.txt"
echo Disabled > ".settings\disableConfirmation.txt"
echo Disabled > ".settings\disableTips.txt"

:: Create fake IObit Unlocker
if not exist "fake_iobit" mkdir fake_iobit
echo @echo off > "fake_iobit\IObitUnlocker.exe"
echo exit /b 0 >> "fake_iobit\IObitUnlocker.exe"
echo. > "fake_iobit\IObitUnlocker.dll"
echo %cd%\fake_iobit > ".settings\customIObitUnlockerPath.txt"

:MENU
cls
echo.
echo ================================================
echo           Minecraft Shader Tool
echo ================================================
echo.

:: Count shaders
set "SHADER_COUNT=0"
for %%f in ("%MCPACKS_DIR%\*.mcpack" "%MCPACKS_DIR%\*.zip") do (
    set /a SHADER_COUNT+=1
)

echo Shaders found: %SHADER_COUNT%
echo.

if %SHADER_COUNT% equ 0 (
    echo [1] Add Shader
    echo [2] Open Minecraft
    echo [3] Exit
    echo.
    choice /c 123 /n /m "Choose option: "
    if errorlevel 3 exit
    if errorlevel 2 goto OPEN_MINECRAFT
    if errorlevel 1 goto ADD_SHADER
) else (
    echo [1] Add New Shader
    echo [2] Apply Shader
    echo [3] Open Minecraft
    echo [4] Remove All Shaders
    echo [5] Exit
    echo.
    choice /c 12345 /n /m "Choose option: "
    if errorlevel 5 exit
    if errorlevel 4 goto REMOVE_ALL
    if errorlevel 3 goto OPEN_MINECRAFT
    if errorlevel 2 goto APPLY_SHADER
    if errorlevel 1 goto ADD_SHADER
)

:ADD_SHADER
cls
echo.
echo ================================================
echo              Add New Shader
echo ================================================
echo.
echo Opening shaders folder...
echo Put your shader file (.mcpack or .zip) in the folder
echo Then press any key to continue...
echo.
start "" explorer "%MCPACKS_DIR%"
pause >nul
goto MENU

:APPLY_SHADER
cls
echo.
echo ================================================
echo              Apply Shader
echo ================================================
echo.
echo Available shaders:
echo.

set "counter=0"
for %%f in ("%MCPACKS_DIR%\*.mcpack" "%MCPACKS_DIR%\*.zip") do (
    set /a counter+=1
    echo [!counter!] %%~nxf
    set "shader_!counter!=%%f"
)

echo.
echo [0] Back to main menu
echo.
set /p "choice=Choose shader number: "

if "%choice%"=="0" goto MENU
if %choice% gtr %counter% goto APPLY_SHADER
if %choice% lss 1 goto APPLY_SHADER

:: Apply selected shader
set "selected=!shader_%choice%!"
echo.
echo Applying shader: !selected!
echo.

:: Clean materials folder
del "%MATERIALS_DIR%\*.material.bin" >nul 2>&1

:: Extract shader
if exist "temp_extract" rmdir /s /q "temp_extract" >nul 2>&1
mkdir "temp_extract"

echo Extracting shader...
powershell -command "Expand-Archive -LiteralPath '!selected!' -DestinationPath 'temp_extract' -Force" >nul 2>&1

:: Find and copy materials
echo Looking for shader files...
set "found=0"

if exist "temp_extract\renderer\materials\*.material.bin" (
    copy "temp_extract\renderer\materials\*.material.bin" "%MATERIALS_DIR%\" >nul 2>&1
    set "found=1"
    goto MATERIALS_FOUND
)

for /d %%d in ("temp_extract\*") do (
    if exist "%%d\renderer\materials\*.material.bin" (
        copy "%%d\renderer\materials\*.material.bin" "%MATERIALS_DIR%\" >nul 2>&1
        set "found=1"
        goto MATERIALS_FOUND
    )
)

for /d %%d in ("temp_extract\subpacks\*") do (
    if exist "%%d\renderer\materials\*.material.bin" (
        copy "%%d\renderer\materials\*.material.bin" "%MATERIALS_DIR%\" >nul 2>&1
        set "found=1"
        goto MATERIALS_FOUND
    )
)

:MATERIALS_FOUND
rmdir /s /q "temp_extract" >nul 2>&1

if "%found%"=="0" (
    echo ERROR: No shader files found!
    echo Make sure this is a valid Minecraft Bedrock shader.
    pause
    goto MENU
)

:: Apply shader using Matject
echo Applying shader to Minecraft...
call matject.bat placebo >nul 2>&1

echo.
echo SUCCESS: Shader applied successfully!
echo.
echo Open Minecraft now?
choice /c yn /n /m "[Y] Yes  [N] No: "
if errorlevel 2 goto MENU
if errorlevel 1 goto OPEN_MINECRAFT

:OPEN_MINECRAFT
echo.
echo Opening Minecraft...
start "" shell:AppsFolder\Microsoft.MinecraftUWP_8wekyb3d8bbwe!App >nul 2>&1
echo.
echo Minecraft opened successfully!
echo.
echo IMPORTANT: Don't forget to enable the shader in Global Resource Packs!
echo.
pause
goto MENU

:REMOVE_ALL
cls
echo.
echo ================================================
echo            Remove All Shaders
echo ================================================
echo.
echo WARNING: This will remove all applied shaders
echo and restore default Minecraft materials.
echo.
choice /c yn /n /m "Are you sure? [Y] Yes  [N] No: "
if errorlevel 2 goto MENU

echo.
echo Removing shaders and restoring defaults...
del "%MATERIALS_DIR%\*.material.bin" >nul 2>&1
call matject.bat placebo >nul 2>&1
echo.
echo SUCCESS: Default materials restored!
pause
goto MENU

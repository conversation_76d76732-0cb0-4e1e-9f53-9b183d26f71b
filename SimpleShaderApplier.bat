@echo off
title Simple Shader Applier - No IObit Required
color 0B

:: Check admin
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Run as Administrator required!
    pause
    exit
)

:: Get Minecraft path
for /f "tokens=*" %%i in ('powershell -command "(Get-AppxPackage -Name Microsoft.MinecraftUWP).InstallLocation"') do set "MC=%%i"
if "%MC%"=="" (
    echo ERROR: Minecraft not found!
    pause
    exit
)

set "MAT=%MC%\data\renderer\materials"
if not exist "SHADERS" mkdir SHADERS
if not exist "BACKUP" mkdir BACKUP

:START
cls
echo.
echo Simple Shader Applier
echo ====================
echo.

set /a count=0
for %%f in ("SHADERS\*.mcpack" "SHADERS\*.zip") do set /a count+=1

echo Minecraft: %MC%
echo Shaders: %count%
echo.
echo [1] Add Shader
if %count% gtr 0 echo [2] Use Shader
echo [3] Remove Shader
echo [4] Open Game
echo [5] Exit
echo.

if %count% gtr 0 (
    choice /c 12345 /n
    if errorlevel 5 exit
    if errorlevel 4 goto GAME
    if errorlevel 3 goto REMOVE
    if errorlevel 2 goto USE
    if errorlevel 1 goto ADD
) else (
    choice /c 1345 /n
    if errorlevel 4 exit
    if errorlevel 3 goto GAME
    if errorlevel 2 goto REMOVE
    if errorlevel 1 goto ADD
)

:ADD
echo.
echo Put shader files in SHADERS folder
start explorer SHADERS
pause
goto START

:USE
cls
echo.
echo Choose Shader:
echo.

:: Backup original if not exists
if not exist "BACKUP\*.material.bin" (
    echo Creating backup...
    copy "%MAT%\*.material.bin" "BACKUP\" >nul 2>&1
)

set /a n=0
for %%f in ("SHADERS\*.mcpack" "SHADERS\*.zip") do (
    set /a n+=1
    echo [!n!] %%~nxf
    set "file!n!=%%f"
)

echo.
set /p c="Number: "
if %c% gtr %n% goto USE
if %c% lss 1 goto USE

set "shader=!file%c%!"
echo.
echo Applying: !shader!

if exist temp rmdir /s /q temp
mkdir temp

powershell "Expand-Archive '!shader!' temp" >nul 2>&1

set found=0
if exist "temp\renderer\materials\*.material.bin" (
    copy "temp\renderer\materials\*.material.bin" "%MAT%\" >nul 2>&1
    set found=1
)

if !found!==0 (
    for /d %%d in (temp\*) do (
        if exist "%%d\renderer\materials\*.material.bin" (
            copy "%%d\renderer\materials\*.material.bin" "%MAT%\" >nul 2>&1
            set found=1
        )
    )
)

if !found!==0 (
    for /d %%d in (temp\subpacks\*) do (
        if exist "%%d\renderer\materials\*.material.bin" (
            copy "%%d\renderer\materials\*.material.bin" "%MAT%\" >nul 2>&1
            set found=1
        )
    )
)

rmdir /s /q temp

if !found!==0 (
    echo ERROR: No shader files found!
    pause
    goto START
)

echo SUCCESS! Shader applied.
echo Open game and enable shader in Global Resource Packs.
pause
goto START

:REMOVE
echo.
if not exist "BACKUP\*.material.bin" (
    echo No backup found!
    pause
    goto START
)

echo Removing shader and restoring original...
copy "BACKUP\*.material.bin" "%MAT%\" >nul 2>&1
echo Done! Original materials restored.
pause
goto START

:GAME
start shell:AppsFolder\Microsoft.MinecraftUWP_8wekyb3d8bbwe!App
echo Game opening... Enable shader in settings!
pause
goto START

@echo off
chcp 65001 >nul
title 🎮 تطبيق الشيدرات - Minecraft Shader Tool
color 0B

:: إعداد سريع
if not exist "matject.bat" (
    echo ❌ ضع هذا الملف في مجلد Matject
    pause & exit
)

:: تفعيل الوضع المباشر
if not exist ".settings" mkdir .settings
echo تم التفعيل > ".settings\directWriteMode.txt"
echo تم التعطيل > ".settings\disableConfirmation.txt"

:: إنشاء مجلدات
if not exist "MCPACKS" mkdir MCPACKS
if not exist "MATERIALS" mkdir MATERIALS

:: إعداد IObit وهمي
if not exist "fake_iobit" mkdir fake_iobit
echo @echo off > fake_iobit\IObitUnlocker.exe
echo exit /b 0 >> fake_iobit\IObitUnlocker.exe
echo. > fake_iobit\IObitUnlocker.dll
echo %cd%\fake_iobit > .settings\customIObitUnlockerPath.txt

:START
cls
echo.
echo     ╔═══════════════════════════════════════════╗
echo     ║        🎮 أداة تطبيق الشيدرات 🎮          ║
echo     ║         Minecraft Shader Tool           ║
echo     ╚═══════════════════════════════════════════╝
echo.

:: عد الشيدرات
set count=0
for %%f in ("MCPACKS\*.mcpack" "MCPACKS\*.zip") do set /a count+=1

if %count%==0 (
    echo     📁 لا توجد شيدرات حالياً
    echo.
    echo     ┌─────────────────────────────────────────┐
    echo     │  [1] 📥 إضافة شيدر                      │
    echo     │  [2] 🎮 فتح Minecraft                   │
    echo     │  [3] 🚪 خروج                           │
    echo     └─────────────────────────────────────────┘
    echo.
    choice /c 123 /n /m "     اختر رقم: "
    if errorlevel 3 exit
    if errorlevel 2 goto MINECRAFT
    if errorlevel 1 goto ADD
) else (
    echo     📁 الشيدرات الموجودة: %count%
    echo.
    echo     ┌─────────────────────────────────────────┐
    echo     │  [1] 📥 إضافة شيدر جديد                  │
    echo     │  [2] ✅ تطبيق شيدر                      │
    echo     │  [3] 🎮 فتح Minecraft                   │
    echo     │  [4] 🔄 إزالة الشيدرات                  │
    echo     │  [5] 🚪 خروج                           │
    echo     └─────────────────────────────────────────┘
    echo.
    choice /c 12345 /n /m "     اختر رقم: "
    if errorlevel 5 exit
    if errorlevel 4 goto REMOVE
    if errorlevel 3 goto MINECRAFT
    if errorlevel 2 goto APPLY
    if errorlevel 1 goto ADD
)

:ADD
cls
echo.
echo     📥 إضافة شيدر جديد
echo     ═══════════════════════════════════════
echo.
echo     🔸 سيتم فتح مجلد الشيدرات
echo     🔸 ضع ملف الشيدر (.mcpack أو .zip)
echo     🔸 ثم اضغط أي زر للمتابعة
echo.
start "" explorer "MCPACKS"
pause >nul
goto START

:APPLY
cls
echo.
echo     ✅ تطبيق الشيدر
echo     ═══════════════════════════════════════
echo.

:: عرض قائمة الشيدرات
set num=0
for %%f in ("MCPACKS\*.mcpack" "MCPACKS\*.zip") do (
    set /a num+=1
    echo     [!num!] %%~nxf
    set "file!num!=%%f"
)

echo.
echo     [0] العودة
echo.
set /p choice="     اختر رقم الشيدر: "

if "%choice%"=="0" goto START
if %choice% gtr %num% goto APPLY
if %choice% lss 1 goto APPLY

:: تطبيق الشيدر
set "selected=!file%choice%!"
echo.
echo     ⏳ جاري تطبيق: !selected!
echo.

:: تنظيف وإعداد
del "MATERIALS\*.material.bin" >nul 2>&1
if exist "temp" rmdir /s /q "temp" >nul 2>&1
mkdir "temp"

:: استخراج
powershell -command "Expand-Archive -LiteralPath '!selected!' -DestinationPath 'temp' -Force" >nul 2>&1

:: نسخ المواد
if exist "temp\renderer\materials\*.material.bin" (
    copy "temp\renderer\materials\*.material.bin" "MATERIALS\" >nul
) else (
    for /d %%d in ("temp\*") do (
        if exist "%%d\renderer\materials\*.material.bin" (
            copy "%%d\renderer\materials\*.material.bin" "MATERIALS\" >nul
            goto FOUND
        )
    )
    for /d %%d in ("temp\subpacks\*") do (
        if exist "%%d\renderer\materials\*.material.bin" (
            copy "%%d\renderer\materials\*.material.bin" "MATERIALS\" >nul
            goto FOUND
        )
    )
    echo     ❌ لم يتم العثور على ملفات الشيدر
    rmdir /s /q "temp" >nul 2>&1
    pause & goto START
)

:FOUND
rmdir /s /q "temp" >nul 2>&1

:: تطبيق عبر Matject
echo     🚀 تطبيق على Minecraft...
call matject.bat placebo >nul 2>&1

echo.
echo     ✅ تم تطبيق الشيدر بنجاح!
echo.
echo     🎮 فتح Minecraft الآن؟
choice /c yn /n /m "     [Y] نعم  [N] لا: "
if errorlevel 2 goto START
if errorlevel 1 goto MINECRAFT

:MINECRAFT
echo.
echo     🚀 فتح Minecraft...
start "" shell:AppsFolder\Microsoft.MinecraftUWP_8wekyb3d8bbwe!App >nul 2>&1
echo     ✅ تم فتح Minecraft
echo.
echo     💡 لا تنس تفعيل الشيدر في إعدادات الموارد!
echo.
pause
goto START

:REMOVE
cls
echo.
echo     🔄 إزالة الشيدرات
echo     ═══════════════════════════════════════
echo.
echo     ⚠️  هذا سيزيل جميع الشيدرات ويستعيد الافتراضي
echo.
choice /c yn /n /m "     متأكد؟ [Y] نعم  [N] لا: "
if errorlevel 2 goto START

echo.
echo     🔄 جاري الاستعادة...
del "MATERIALS\*.material.bin" >nul 2>&1
call matject.bat placebo >nul 2>&1
echo     ✅ تم استعادة الافتراضي
pause
goto START

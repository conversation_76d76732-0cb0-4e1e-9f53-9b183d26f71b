@echo off
setlocal enabledelayedexpansion
title Quick Shader Tool
color 0A

if not exist "matject.bat" (
    echo ERROR: matject.bat not found!
    echo Put this file in the same folder as matject.bat
    pause
    exit
)

:: Quick setup
if not exist "MCPACKS" mkdir MCPACKS
if not exist "MATERIALS" mkdir MATERIALS
if not exist ".settings" mkdir .settings

echo Enabled > .settings\directWriteMode.txt
echo Disabled > .settings\disableConfirmation.txt

if not exist "fake_iobit" mkdir fake_iobit
echo @echo off > fake_iobit\IObitUnlocker.exe
echo exit /b 0 >> fake_iobit\IObitUnlocker.exe
echo. > fake_iobit\IObitUnlocker.dll
echo %cd%\fake_iobit > .settings\customIObitUnlockerPath.txt

:START
cls
echo.
echo ==========================================
echo         Quick Shader Tool
echo ==========================================
echo.

set count=0
for %%f in ("MCPACKS\*.mcpack" "MCPACKS\*.zip") do set /a count+=1

echo Shaders found: %count%
echo.

if %count%==0 (
    echo [1] Add Shader
    echo [2] Open Minecraft  
    echo [3] Exit
    echo.
    choice /c 123 /n
    if errorlevel 3 exit
    if errorlevel 2 goto MINECRAFT
    if errorlevel 1 goto ADD
) else (
    echo [1] Add Shader
    echo [2] Apply Shader
    echo [3] Open Minecraft
    echo [4] Remove Shaders
    echo [5] Exit
    echo.
    choice /c 12345 /n
    if errorlevel 5 exit
    if errorlevel 4 goto REMOVE
    if errorlevel 3 goto MINECRAFT
    if errorlevel 2 goto APPLY
    if errorlevel 1 goto ADD
)

:ADD
cls
echo.
echo Adding Shader...
echo.
echo Opening MCPACKS folder...
echo Put your shader file (.mcpack or .zip) there
echo Then press any key...
echo.
start "" explorer "MCPACKS"
pause >nul
goto START

:APPLY
cls
echo.
echo Apply Shader
echo.

set num=0
for %%f in ("MCPACKS\*.mcpack" "MCPACKS\*.zip") do (
    set /a num+=1
    echo [!num!] %%~nxf
    set "file!num!=%%f"
)

echo.
echo [0] Back
set /p choice="Choose: "

if "%choice%"=="0" goto START
if %choice% gtr %num% goto APPLY
if %choice% lss 1 goto APPLY

set "selected=!file%choice%!"
echo.
echo Applying: !selected!

del "MATERIALS\*.material.bin" >nul 2>&1
if exist "temp" rmdir /s /q "temp" >nul 2>&1
mkdir "temp"

powershell -command "Expand-Archive -LiteralPath '!selected!' -DestinationPath 'temp' -Force" >nul 2>&1

if exist "temp\renderer\materials\*.material.bin" (
    copy "temp\renderer\materials\*.material.bin" "MATERIALS\" >nul
    goto FOUND
)

for /d %%d in ("temp\*") do (
    if exist "%%d\renderer\materials\*.material.bin" (
        copy "%%d\renderer\materials\*.material.bin" "MATERIALS\" >nul
        goto FOUND
    )
)

for /d %%d in ("temp\subpacks\*") do (
    if exist "%%d\renderer\materials\*.material.bin" (
        copy "%%d\renderer\materials\*.material.bin" "MATERIALS\" >nul
        goto FOUND
    )
)

echo ERROR: No shader files found!
rmdir /s /q "temp" >nul 2>&1
pause
goto START

:FOUND
rmdir /s /q "temp" >nul 2>&1
echo Applying to Minecraft...
call matject.bat placebo >nul 2>&1
echo.
echo SUCCESS! Shader applied.
echo.
echo Open Minecraft now? [Y/N]
choice /c yn /n
if errorlevel 2 goto START
if errorlevel 1 goto MINECRAFT

:MINECRAFT
echo.
echo Opening Minecraft...
start "" shell:AppsFolder\Microsoft.MinecraftUWP_8wekyb3d8bbwe!App >nul 2>&1
echo Done! Enable shader in Global Resource Packs.
pause
goto START

:REMOVE
echo.
echo Remove all shaders? [Y/N]
choice /c yn /n
if errorlevel 2 goto START
echo Removing...
del "MATERIALS\*.material.bin" >nul 2>&1
call matject.bat placebo >nul 2>&1
echo Done! Default materials restored.
pause
goto START

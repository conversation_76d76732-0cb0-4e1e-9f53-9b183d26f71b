@echo off
setlocal enabledelayedexpansion
title Test IObit Choice Feature

:: Test colors
set "RED=[91m"
set "GRN=[92m"
set "YLW=[93m"
set "BLU=[94m"
set "CYN=[96m"
set "WHT=[97m"
set "GRY=[90m"
set "RST=[0m"

echo !CYN!╔══════════════════════════════════════════════════════════════╗!RST!
echo !CYN!║                    !WHT!اختبار ميزة IObit!CYN!                        ║!RST!
echo !CYN!╚══════════════════════════════════════════════════════════════╝!RST!
echo.

:: Test IObit mode display
if exist ".settings\iobitModeSelected.txt" (
    set /p currentIObitMode=<".settings\iobitModeSelected.txt"
    if "!currentIObitMode!"=="iobit" (
        set "iobitModeDisplay=!GRN!IObit Unlocker!RST!"
    ) else (
        set "iobitModeDisplay=!BLU!Direct Write Mode!RST!"
    )
    echo !YLW!الطريقة الحالية:!RST! !iobitModeDisplay!
) else (
    set "iobitModeDisplay=!RED!غير محدد!RST!"
    echo !YLW!الطريقة الحالية:!RST! !iobitModeDisplay!
)

echo.
echo !GRN![1] تجربة تغيير إلى IObit Unlocker!RST!
echo !BLU![2] تجربة تغيير إلى Direct Write Mode!RST!
echo !RED![3] حذف الإعداد الحالي!RST!
echo !WHT![4] عرض محتوى ملف الإعداد!RST!
echo !YLW![5] خروج!RST!
echo.
choice /c 12345 /n >nul

if !errorlevel! equ 1 (
    if not exist ".settings" mkdir .settings
    echo iobit>".settings\iobitModeSelected.txt"
    if exist ".settings\directWriteMode.txt" del /q ".settings\directWriteMode.txt" >nul
    echo !GRN![*] تم تعيين IObit Unlocker!RST!
    pause
    goto :eof
)

if !errorlevel! equ 2 (
    if not exist ".settings" mkdir .settings
    echo direct>".settings\iobitModeSelected.txt"
    echo being outside WindowsApps feels like freedom. [%date% // %time:~0,-6%]>".settings\directWriteMode.txt"
    echo !BLU![*] تم تعيين Direct Write Mode!RST!
    pause
    goto :eof
)

if !errorlevel! equ 3 (
    if exist ".settings\iobitModeSelected.txt" (
        del /q ".settings\iobitModeSelected.txt" >nul
        echo !RED![*] تم حذف الإعداد!RST!
    ) else (
        echo !YLW![*] لا يوجد إعداد لحذفه!RST!
    )
    if exist ".settings\directWriteMode.txt" (
        del /q ".settings\directWriteMode.txt" >nul
        echo !RED![*] تم حذف ملف Direct Write Mode!RST!
    )
    pause
    goto :eof
)

if !errorlevel! equ 4 (
    echo.
    echo !WHT!محتوى ملف الإعداد:!RST!
    if exist ".settings\iobitModeSelected.txt" (
        type ".settings\iobitModeSelected.txt"
    ) else (
        echo !RED!الملف غير موجود!RST!
    )
    echo.
    echo !WHT!ملف Direct Write Mode:!RST!
    if exist ".settings\directWriteMode.txt" (
        echo !GRN!موجود!RST!
    ) else (
        echo !RED!غير موجود!RST!
    )
    pause
    goto :eof
)

if !errorlevel! equ 5 exit

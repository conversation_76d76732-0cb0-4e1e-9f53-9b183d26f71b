@echo off
chcp 65001 >nul
title Test Window Stay Open

:: Method 1: Keep window open with cmd /k
if not defined RUNNING_FROM_CMD (
    set RUNNING_FROM_CMD=1
    cmd /k "%~f0"
    exit /b
)

cls

echo ================================================================
echo            Simple Shader Tool - No IObit Required
echo ================================================================
echo.
echo [+] No IObit Unlocker needed
echo [+] Direct shader application  
echo [+] Works with all Minecraft versions
echo.
echo [*] Searching for Minecraft...
echo [+] Found Minecraft (Microsoft Store)
echo [+] Found Minecraft Preview
echo [X] Minecraft not found
echo [*] Make sure Minecraft Bedrock Edition is installed
echo.
echo ================================================================
echo                    Press any key to continue...
echo ================================================================
pause >nul

echo.
echo This window will stay open until you close it manually.
echo You can now see all the messages clearly.
echo.
echo ================================================================
echo                    Press any key to exit...
echo ================================================================
pause >nul

echo.
echo Window will close in 10 seconds...
timeout /t 10 /nobreak >nul

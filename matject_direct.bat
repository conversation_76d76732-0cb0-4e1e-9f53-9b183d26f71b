@echo off
chcp 437 >nul
setlocal enabledelayedexpansion
title Simple Shader Tool - No IObit Required

:: Prevent window from closing immediately
set "PAUSE_ON_ERROR=1"
set "PAUSE_ON_SUCCESS=1"

:: Keep window open with cmd /k
if not defined RUNNING_FROM_CMD (
    set RUNNING_FROM_CMD=1
    cmd /k "%~f0"
    exit /b
)

cls

:: Enhanced Colors with proper escape sequences
for /F %%a in ('echo prompt $E ^| cmd') do set "ESC=%%a"
set "RED=%ESC%[91m"
set "GRN=%ESC%[92m"
set "YLW=%ESC%[93m"
set "BLU=%ESC%[94m"
set "CYN=%ESC%[96m"
set "WHT=%ESC%[97m"
set "GRY=%ESC%[90m"
set "RST=%ESC%[0m"
set "BOLD=%ESC%[1m"
set "UNDERLINE=%ESC%[4m"

echo %CYN%================================================================%RST%
echo %CYN%            %WHT%Simple Shader Tool - No IObit Required%CYN%              %RST%
echo %CYN%================================================================%RST%
echo.
echo %GRN%[+] No IObit Unlocker needed%RST%
echo %GRN%[+] Direct shader application%RST%
echo %GRN%[+] Works with all Minecraft versions%RST%
echo.

:: Find Minecraft installation
echo %YLW%[*] Searching for Minecraft...%RST%

set "MINECRAFT_PATH="
set "MATERIALS_PATH="

:: Check common Minecraft locations
if exist "%LOCALAPPDATA%\Packages\Microsoft.MinecraftUWP_8wekyb3d8bbwe\LocalState\games\com.mojang" (
    set "MINECRAFT_PATH=%LOCALAPPDATA%\Packages\Microsoft.MinecraftUWP_8wekyb3d8bbwe\LocalState\games\com.mojang"
    set "MATERIALS_PATH=!MINECRAFT_PATH!\resource_packs"
    echo %GRN%[+] Found Minecraft (Microsoft Store)%RST%
) else if exist "%LOCALAPPDATA%\Packages\Microsoft.MinecraftWindowsBeta_8wekyb3d8bbwe\LocalState\games\com.mojang" (
    set "MINECRAFT_PATH=%LOCALAPPDATA%\Packages\Microsoft.MinecraftWindowsBeta_8wekyb3d8bbwe\LocalState\games\com.mojang"
    set "MATERIALS_PATH=!MINECRAFT_PATH!\resource_packs"
    echo %GRN%[+] Found Minecraft Preview%RST%
) else (
    echo %RED%[X] Minecraft not found%RST%
    echo %YLW%[*] Make sure Minecraft Bedrock Edition is installed%RST%
    echo.
    echo %CYN%Press any key to exit...%RST%
    pause >nul
    exit /b 1
)

echo %GRN%[✓] Minecraft Path: %MINECRAFT_PATH%%RST%
echo.

:: Check for shader files
echo %YLW%[*] Searching for shader files...%RST%

if not exist "MATERIALS\*.material.bin" (
    if not exist "MCPACKS\*.mcpack" (
        if not exist "MCPACKS\*.zip" (
            echo %RED%[✗] No shader files found%RST%
            echo.
            echo %YLW%[*] Place .material.bin files in MATERIALS folder%RST%
            echo %YLW%[*] Or place .mcpack/.zip files in MCPACKS folder%RST%
            echo.
            echo %CYN%Press any key to exit...%RST%
            pause >nul
            exit /b 1
        )
    )
)

:: Extract MCPACK if needed
if exist "MCPACKS\*.mcpack" (
    echo %YLW%[*] Extracting MCPACK files...%RST%

    if not exist "temp_extract" mkdir "temp_extract"

    for %%f in ("MCPACKS\*.mcpack") do (
        echo %GRN%[*] Extracting: %%~nf%RST%
        :: Rename .mcpack to .zip temporarily for extraction
        copy "%%f" "temp_extract\%%~nf.zip" >nul
        powershell -Command "Expand-Archive -Path 'temp_extract\%%~nf.zip' -DestinationPath 'temp_extract\%%~nf' -Force"
        del "temp_extract\%%~nf.zip" >nul 2>&1

        :: Copy materials
        if exist "temp_extract\%%~nf\renderer\materials\*.material.bin" (
            if not exist "MATERIALS" mkdir "MATERIALS"
            copy /Y "temp_extract\%%~nf\renderer\materials\*.material.bin" "MATERIALS\" >nul
            echo %GRN%[✓] Material files copied successfully%RST%
        )
    )

    rmdir /s /q "temp_extract" 2>nul
)

if exist "MCPACKS\*.zip" (
    echo %YLW%[*] Extracting ZIP files...%RST%

    if not exist "temp_extract" mkdir "temp_extract"

    for %%f in ("MCPACKS\*.zip") do (
        echo %GRN%[*] Extracting: %%~nf%RST%
        powershell -Command "Expand-Archive -Path '%%f' -DestinationPath 'temp_extract\%%~nf' -Force"
        
        :: Copy materials
        if exist "temp_extract\%%~nf\renderer\materials\*.material.bin" (
            if not exist "MATERIALS" mkdir "MATERIALS"
            copy /Y "temp_extract\%%~nf\renderer\materials\*.material.bin" "MATERIALS\" >nul
            echo %GRN%[✓] Material files copied successfully%RST%
        )
    )
    
    rmdir /s /q "temp_extract" 2>nul
)

:: Count material files
set "MATERIAL_COUNT=0"
for %%f in ("MATERIALS\*.material.bin") do (
    set /a MATERIAL_COUNT+=1
)

if !MATERIAL_COUNT! equ 0 (
    echo %RED%[✗] No .material.bin files found%RST%
    echo.
    echo %CYN%Press any key to exit...%RST%
    pause >nul
    exit /b 1
)

echo %GRN%[✓] Found %MATERIAL_COUNT% material files%RST%
echo.

:: Create backup
echo %YLW%[*] Creating backup...%RST%

set "BACKUP_DIR=BACKUPS\backup_%date:~-4%-%date:~-10,2%-%date:~-7,2%_%time:~0,2%-%time:~3,2%-%time:~6,2%"
set "BACKUP_DIR=!BACKUP_DIR: =0!"

if not exist "BACKUPS" mkdir "BACKUPS"
if not exist "!BACKUP_DIR!" mkdir "!BACKUP_DIR!"

:: Find Minecraft app folder
for /d %%d in ("%ProgramFiles%\WindowsApps\Microsoft.MinecraftUWP*") do (
    if exist "%%d\data\renderer\materials" (
        echo %GRN%[✓] Backup from: %%d%RST%
        xcopy /E /I /Y "%%d\data\renderer\materials" "!BACKUP_DIR!\materials" >nul
        set "MINECRAFT_APP_PATH=%%d"
        goto :backup_done
    )
)

for /d %%d in ("%ProgramFiles%\WindowsApps\Microsoft.MinecraftWindowsBeta*") do (
    if exist "%%d\data\renderer\materials" (
        echo %GRN%[✓] Backup from: %%d%RST%
        xcopy /E /I /Y "%%d\data\renderer\materials" "!BACKUP_DIR!\materials" >nul
        set "MINECRAFT_APP_PATH=%%d"
        goto :backup_done
    )
)

echo %RED%[✗] Minecraft app folder not found%RST%
echo.
echo %CYN%Press any key to exit...%RST%
pause >nul
exit /b 1

:backup_done
echo %GRN%[✓] Backup created successfully%RST%
echo.

:: Apply shaders
echo %YLW%[*] Applying shaders...%RST%

for %%f in ("MATERIALS\*.material.bin") do (
    echo %GRN%[*] Applying: %%~nf%RST%
    copy /Y "%%f" "!MINECRAFT_APP_PATH!\data\renderer\materials\" >nul 2>&1
    if !errorlevel! equ 0 (
        echo %GRN%[✓] Successfully applied %%~nf%RST%
    ) else (
        echo %YLW%[!] Failed to apply %%~nf - Trying with elevated permissions...%RST%
        
        :: Try with takeown and icacls
        takeown /f "!MINECRAFT_APP_PATH!\data\renderer\materials\%%~nxf" >nul 2>&1
        icacls "!MINECRAFT_APP_PATH!\data\renderer\materials\%%~nxf" /grant "%USERNAME%:F" >nul 2>&1
        copy /Y "%%f" "!MINECRAFT_APP_PATH!\data\renderer\materials\" >nul 2>&1
        
        if !errorlevel! equ 0 (
            echo %GRN%[✓] Successfully applied %%~nf (with elevated permissions)%RST%
        ) else (
            echo %RED%[✗] Failed to apply %%~nf%RST%
        )
    )
)

echo.
echo %GRN%================================================================%RST%
echo %GRN%                        %WHT%COMPLETED%GRN%                          %RST%
echo %GRN%================================================================%RST%
echo.
echo %GRN%[✓] Shaders applied successfully!%RST%
echo %YLW%[*] You can now run Minecraft and enjoy the shaders!%RST%
echo.
echo %CYN%[*] Backup saved in: %BACKUP_DIR%%RST%
echo.
echo %WHT%================================================================%RST%
echo %CYN%                    Press any key to exit...%RST%
echo %WHT%================================================================%RST%
pause >nul

:: Additional safety measure to prevent window closing
echo.
echo %GRN%Window will close in 5 seconds...%RST%
timeout /t 5 /nobreak >nul

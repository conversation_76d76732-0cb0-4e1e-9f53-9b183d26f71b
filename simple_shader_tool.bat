@echo off
setlocal enabledelayedexpansion
title Simple Shader Tool - No IObit Required
cls

echo ================================================================
echo            Simple Shader Tool - No IObit Required              
echo ================================================================
echo.
echo [+] No IObit Unlocker needed
echo [+] Direct shader application  
echo [+] Works with all Minecraft versions
echo.

:: Find Minecraft installation
echo [*] Searching for Minecraft...

set "MINECRAFT_PATH="
set "MINECRAFT_APP_PATH="

:: Check common Minecraft locations
if exist "%LOCALAPPDATA%\Packages\Microsoft.MinecraftUWP_8wekyb3d8bbwe\LocalState\games\com.mojang" (
    set "MINECRAFT_PATH=%LOCALAPPDATA%\Packages\Microsoft.MinecraftUWP_8wekyb3d8bbwe\LocalState\games\com.mojang"
    echo [+] Found Minecraft (Microsoft Store)
) else if exist "%LOCALAPPDATA%\Packages\Microsoft.MinecraftWindowsBeta_8wekyb3d8bbwe\LocalState\games\com.mojang" (
    set "MINECRAFT_PATH=%LOCALAPPDATA%\Packages\Microsoft.MinecraftWindowsBeta_8wekyb3d8bbwe\LocalState\games\com.mojang"
    echo [+] Found Minecraft Preview
) else (
    echo [X] Minecraft not found!
    echo [*] Make sure Minecraft Bedrock Edition is installed
    pause
    exit /b 1
)

echo [+] Minecraft Path: !MINECRAFT_PATH!
echo.

:: Find Minecraft app folder
echo [*] Searching for Minecraft app folder...

for /d %%d in ("%ProgramFiles%\WindowsApps\Microsoft.MinecraftUWP*") do (
    if exist "%%d\data\renderer\materials" (
        set "MINECRAFT_APP_PATH=%%d"
        echo [+] Found Minecraft app: %%d
        goto :app_found
    )
)

for /d %%d in ("%ProgramFiles%\WindowsApps\Microsoft.MinecraftWindowsBeta*") do (
    if exist "%%d\data\renderer\materials" (
        set "MINECRAFT_APP_PATH=%%d"
        echo [+] Found Minecraft Preview app: %%d
        goto :app_found
    )
)

echo [X] Minecraft app folder not found!
pause
exit /b 1

:app_found
echo.

:: Check for shader files
echo [*] Searching for shader files...

:: Extract MCPACK if needed
if exist "MCPACKS\*.mcpack" (
    echo [*] Extracting MCPACK files...
    
    if not exist "temp_extract" mkdir "temp_extract"
    
    for %%f in ("MCPACKS\*.mcpack") do (
        echo [*] Extracting: %%~nf
        :: Copy mcpack as zip and extract
        copy "%%f" "temp_extract\%%~nf.zip" >nul
        powershell -Command "try { Expand-Archive -Path 'temp_extract\%%~nf.zip' -DestinationPath 'temp_extract\%%~nf' -Force } catch { Write-Host 'Failed to extract %%~nf' }"
        del "temp_extract\%%~nf.zip" >nul 2>&1
        
        :: Copy materials
        if exist "temp_extract\%%~nf\renderer\materials\*.material.bin" (
            if not exist "MATERIALS" mkdir "MATERIALS"
            copy /Y "temp_extract\%%~nf\renderer\materials\*.material.bin" "MATERIALS\" >nul
            echo [+] Copied material files from %%~nf
        )
    )
    
    rmdir /s /q "temp_extract" 2>nul
)

:: Extract ZIP if needed
if exist "MCPACKS\*.zip" (
    echo [*] Extracting ZIP files...
    
    if not exist "temp_extract" mkdir "temp_extract"
    
    for %%f in ("MCPACKS\*.zip") do (
        echo [*] Extracting: %%~nf
        powershell -Command "Expand-Archive -Path '%%f' -DestinationPath 'temp_extract\%%~nf' -Force"
        
        :: Copy materials
        if exist "temp_extract\%%~nf\renderer\materials\*.material.bin" (
            if not exist "MATERIALS" mkdir "MATERIALS"
            copy /Y "temp_extract\%%~nf\renderer\materials\*.material.bin" "MATERIALS\" >nul
            echo [+] Copied material files from %%~nf
        )
    )
    
    rmdir /s /q "temp_extract" 2>nul
)

:: Count material files
set "MATERIAL_COUNT=0"
if exist "MATERIALS\*.material.bin" (
    for %%f in ("MATERIALS\*.material.bin") do (
        set /a MATERIAL_COUNT+=1
    )
)

if !MATERIAL_COUNT! equ 0 (
    echo [X] No .material.bin files found!
    echo [*] Put .material.bin files in MATERIALS folder
    echo [*] Or put .mcpack/.zip files in MCPACKS folder
    pause
    exit /b 1
)

echo [+] Found !MATERIAL_COUNT! material files
echo.

:: Create backup
echo [*] Creating backup...

set "BACKUP_DIR=BACKUPS\backup_%date:~-4%-%date:~-10,2%-%date:~-7,2%_%time:~0,2%-%time:~3,2%-%time:~6,2%"
set "BACKUP_DIR=!BACKUP_DIR: =0!"

if not exist "BACKUPS" mkdir "BACKUPS"
if not exist "!BACKUP_DIR!" mkdir "!BACKUP_DIR!"

echo [+] Backing up from: !MINECRAFT_APP_PATH!
xcopy /E /I /Y "!MINECRAFT_APP_PATH!\data\renderer\materials" "!BACKUP_DIR!\materials" >nul
echo [+] Backup created successfully
echo.

:: Apply shaders
echo [*] Applying shaders...

for %%f in ("MATERIALS\*.material.bin") do (
    echo [*] Applying: %%~nf
    copy /Y "%%f" "!MINECRAFT_APP_PATH!\data\renderer\materials\" >nul 2>&1
    if !errorlevel! equ 0 (
        echo [+] Applied %%~nf successfully
    ) else (
        echo [!] Failed to apply %%~nf - trying with elevated permissions...
        
        :: Try with takeown and icacls
        takeown /f "!MINECRAFT_APP_PATH!\data\renderer\materials\%%~nxf" >nul 2>&1
        icacls "!MINECRAFT_APP_PATH!\data\renderer\materials\%%~nxf" /grant "%USERNAME%:F" >nul 2>&1
        copy /Y "%%f" "!MINECRAFT_APP_PATH!\data\renderer\materials\" >nul 2>&1
        
        if !errorlevel! equ 0 (
            echo [+] Applied %%~nf successfully (with elevated permissions)
        ) else (
            echo [X] Failed to apply %%~nf
        )
    )
)

echo.
echo ================================================================
echo                        COMPLETED                               
echo ================================================================
echo.
echo [+] Shaders applied successfully!
echo [*] You can now run Minecraft and enjoy the shaders
echo.
echo [*] Backup saved in: !BACKUP_DIR!
echo.

pause

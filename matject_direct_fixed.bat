@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion
title Simple Shader Tool - No IObit Required

:: Prevent window from closing immediately
set "PAUSE_ON_ERROR=1"
set "PAUSE_ON_SUCCESS=1"

:: Trap errors to prevent window closing
if not defined RUNNING_FROM_CMD (
    set RUNNING_FROM_CMD=1
    cmd /k "%~f0"
    exit /b
)

cls

:: Colors
set "RED=[91m"
set "GRN=[92m"
set "YLW=[93m"
set "BLU=[94m"
set "CYN=[96m"
set "WHT=[97m"
set "GRY=[90m"
set "RST=[0m"

echo !CYN!================================================================!RST!
echo !CYN!           !WHT!Simple Shader Tool - No IObit Required!CYN!
echo !CYN!================================================================!RST!
echo.
echo !GRN![+] No IObit Unlocker needed!RST!
echo !GRN![+] Direct shader application!RST!
echo !GRN![+] Works with all Minecraft versions!RST!
echo.

:: Find Minecraft installation
echo !YLW![*] Searching for Minecraft...!RST!

set "MINECRAFT_PATH="
set "MATERIALS_PATH="

:: Check common Minecraft locations
if exist "%LOCALAPPDATA%\Packages\Microsoft.MinecraftUWP_8wekyb3d8bbwe\LocalState\games\com.mojang" (
    set "MINECRAFT_PATH=%LOCALAPPDATA%\Packages\Microsoft.MinecraftUWP_8wekyb3d8bbwe\LocalState\games\com.mojang"
    set "MATERIALS_PATH=!MINECRAFT_PATH!\resource_packs"
    echo !GRN![+] Found Minecraft ^(Microsoft Store^)!RST!
) else if exist "%LOCALAPPDATA%\Packages\Microsoft.MinecraftWindowsBeta_8wekyb3d8bbwe\LocalState\games\com.mojang" (
    set "MINECRAFT_PATH=%LOCALAPPDATA%\Packages\Microsoft.MinecraftWindowsBeta_8wekyb3d8bbwe\LocalState\games\com.mojang"
    set "MATERIALS_PATH=!MINECRAFT_PATH!\resource_packs"
    echo !GRN![+] Found Minecraft Preview!RST!
) else (
    echo !RED![X] Minecraft not found!RST!
    echo !YLW![*] Make sure Minecraft Bedrock Edition is installed!RST!
    echo.
    echo !WHT!================================================================!RST!
    echo !CYN!                    Press any key to exit...!RST!
    echo !WHT!================================================================!RST!
    pause >nul
    goto :end_script
)

echo !GRN![✓] مسار Minecraft: !MINECRAFT_PATH!!RST!
echo.

:: Check for shader files
echo !YLW![*] البحث عن ملفات الشيدرات...!RST!

if not exist "MATERIALS\*.material.bin" (
    if not exist "MCPACKS\*.mcpack" (
        if not exist "MCPACKS\*.zip" (
            echo !RED![✗] لم يتم العثور على ملفات شيدرات!RST!
            echo.
            echo !YLW![*] ضع ملفات .material.bin في مجلد MATERIALS!RST!
            echo !YLW![*] أو ضع ملفات .mcpack/.zip في مجلد MCPACKS!RST!
            echo.
            echo !WHT!================================================================!RST!
            echo !CYN!                    Press any key to exit...!RST!
            echo !WHT!================================================================!RST!
            pause >nul
            goto :end_script
        )
    )
)

:: Extract MCPACK if needed
if exist "MCPACKS\*.mcpack" (
    echo !YLW![*] استخراج ملفات MCPACK...!RST!
    
    if not exist "temp_extract" mkdir "temp_extract"
    
    for %%f in ("MCPACKS\*.mcpack") do (
        echo !GRN![*] استخراج: %%~nf!RST!
        :: Rename .mcpack to .zip temporarily for extraction
        copy "%%f" "temp_extract\%%~nf.zip" >nul
        powershell -Command "Expand-Archive -Path 'temp_extract\%%~nf.zip' -DestinationPath 'temp_extract\%%~nf' -Force"
        del "temp_extract\%%~nf.zip" >nul 2>&1
        
        :: Copy materials
        if exist "temp_extract\%%~nf\renderer\materials\*.material.bin" (
            if not exist "MATERIALS" mkdir "MATERIALS"
            copy /Y "temp_extract\%%~nf\renderer\materials\*.material.bin" "MATERIALS\" >nul
            echo !GRN![✓] تم نسخ ملفات المواد!RST!
        )
    )
    
    rmdir /s /q "temp_extract" 2>nul
)

if exist "MCPACKS\*.zip" (
    echo !YLW![*] استخراج ملفات ZIP...!RST!
    
    if not exist "temp_extract" mkdir "temp_extract"
    
    for %%f in ("MCPACKS\*.zip") do (
        echo !GRN![*] استخراج: %%~nf!RST!
        powershell -Command "Expand-Archive -Path '%%f' -DestinationPath 'temp_extract\%%~nf' -Force"
        
        :: Copy materials
        if exist "temp_extract\%%~nf\renderer\materials\*.material.bin" (
            if not exist "MATERIALS" mkdir "MATERIALS"
            copy /Y "temp_extract\%%~nf\renderer\materials\*.material.bin" "MATERIALS\" >nul
            echo !GRN![✓] تم نسخ ملفات المواد!RST!
        )
    )
    
    rmdir /s /q "temp_extract" 2>nul
)

:: Count material files
set "MATERIAL_COUNT=0"
for %%f in ("MATERIALS\*.material.bin") do (
    set /a MATERIAL_COUNT+=1
)

if !MATERIAL_COUNT! equ 0 (
    echo !RED![✗] لم يتم العثور على ملفات .material.bin!RST!
    echo.
    echo !WHT!================================================================!RST!
    echo !CYN!                    Press any key to exit...!RST!
    echo !WHT!================================================================!RST!
    pause >nul
    goto :end_script
)

echo !GRN![✓] تم العثور على !MATERIAL_COUNT! ملف مواد!RST!
echo.

:: Create backup
echo !YLW![*] إنشاء نسخة احتياطية...!RST!

set "BACKUP_DIR=BACKUPS\backup_%date:~-4%-%date:~-10,2%-%date:~-7,2%_%time:~0,2%-%time:~3,2%-%time:~6,2%"
set "BACKUP_DIR=!BACKUP_DIR: =0!"

if not exist "BACKUPS" mkdir "BACKUPS"
if not exist "!BACKUP_DIR!" mkdir "!BACKUP_DIR!"

:: Find Minecraft app folder
for /d %%d in ("%ProgramFiles%\WindowsApps\Microsoft.MinecraftUWP*") do (
    if exist "%%d\data\renderer\materials" (
        echo !GRN![✓] نسخ احتياطي من: %%d!RST!
        xcopy /E /I /Y "%%d\data\renderer\materials" "!BACKUP_DIR!\materials" >nul
        set "MINECRAFT_APP_PATH=%%d"
        goto :backup_done
    )
)

for /d %%d in ("%ProgramFiles%\WindowsApps\Microsoft.MinecraftWindowsBeta*") do (
    if exist "%%d\data\renderer\materials" (
        echo !GRN![✓] نسخ احتياطي من: %%d!RST!
        xcopy /E /I /Y "%%d\data\renderer\materials" "!BACKUP_DIR!\materials" >nul
        set "MINECRAFT_APP_PATH=%%d"
        goto :backup_done
    )
)

echo !RED![✗] لم يتم العثور على مجلد تطبيق Minecraft!RST!
echo.
echo !WHT!================================================================!RST!
echo !CYN!                    Press any key to exit...!RST!
echo !WHT!================================================================!RST!
pause >nul
goto :end_script

:backup_done
echo !GRN![✓] تم إنشاء النسخة الاحتياطية!RST!
echo.

:: Apply shaders
echo !YLW![*] تطبيق الشيدرات...!RST!

for %%f in ("MATERIALS\*.material.bin") do (
    echo !GRN![*] تطبيق: %%~nf!RST!
    copy /Y "%%f" "!MINECRAFT_APP_PATH!\data\renderer\materials\" >nul 2>&1
    if !errorlevel! equ 0 (
        echo !GRN![✓] تم تطبيق %%~nf بنجاح!RST!
    ) else (
        echo !YLW![!] فشل في تطبيق %%~nf - جاري المحاولة بصلاحيات أعلى...!RST!
        
        :: Try with takeown and icacls
        takeown /f "!MINECRAFT_APP_PATH!\data\renderer\materials\%%~nxf" >nul 2>&1
        icacls "!MINECRAFT_APP_PATH!\data\renderer\materials\%%~nxf" /grant "%USERNAME%:F" >nul 2>&1
        copy /Y "%%f" "!MINECRAFT_APP_PATH!\data\renderer\materials\" >nul 2>&1
        
        if !errorlevel! equ 0 (
            echo !GRN![✓] تم تطبيق %%~nf بنجاح ^(بصلاحيات أعلى^)!RST!
        ) else (
            echo !RED![✗] فشل في تطبيق %%~nf!RST!
        )
    )
)

echo.
echo !GRN!╔══════════════════════════════════════════════════════════════╗!RST!
echo !GRN!║                        !WHT!تم الانتهاء!GRN!                          ║!RST!
echo !GRN!╚══════════════════════════════════════════════════════════════╝!RST!
echo.
echo !GRN![✓] تم تطبيق الشيدرات بنجاح!!RST!
echo !YLW![*] يمكنك الآن تشغيل Minecraft والاستمتاع بالشيدرات!RST!
echo.
echo !CYN![*] النسخة الاحتياطية محفوظة في: !BACKUP_DIR!!RST!
echo.

:end_script
echo !WHT!================================================================!RST!
echo !CYN!                    Press any key to exit...!RST!
echo !WHT!================================================================!RST!
pause >nul

:: Additional safety measure to prevent window closing
echo.
echo !GRN!Window will close in 5 seconds...!RST!
timeout /t 5 /nobreak >nul

@echo off
chcp 65001 >nul
title تشغيل الشيدرات على Minecraft
color 0A

:: فحص وجود Matject
if not exist "matject.bat" (
    echo.
    echo ❌ خطأ: لم يتم العثور على Matject
    echo.
    echo 📋 تأكد من وضع هذا الملف في نفس مجلد matject.bat
    echo.
    pause
    exit
)

:: إعداد تلقائي سريع
if not exist ".settings" mkdir .settings >nul 2>&1
if not exist "MCPACKS" mkdir MCPACKS >nul 2>&1
if not exist "MATERIALS" mkdir MATERIALS >nul 2>&1

:: تفعيل الوضع المباشر (بدون IObit Unlocker)
echo تم التفعيل > ".settings\directWriteMode.txt"
echo تم التعطيل > ".settings\disableConfirmation.txt"
echo تم التعطيل > ".settings\disableTips.txt"

:: إنشاء IObit وهمي
if not exist "fake_iobit" mkdir fake_iobit >nul 2>&1
echo @echo off > fake_iobit\IObitUnlocker.exe
echo exit /b 0 >> fake_iobit\IObitUnlocker.exe
echo. > fake_iobit\IObitUnlocker.dll
echo %cd%\fake_iobit > .settings\customIObitUnlockerPath.txt

:MENU
cls
echo.
echo        ████████████████████████████████████████
echo        █                                      █
echo        █       🎮 تشغيل الشيدرات 🎮           █
echo        █        Minecraft Shaders            █
echo        █                                      █
echo        ████████████████████████████████████████
echo.

:: فحص الشيدرات الموجودة
set shader_count=0
for %%f in ("MCPACKS\*.mcpack" "MCPACKS\*.zip") do (
    set /a shader_count+=1
)

if %shader_count%==0 (
    echo        📁 حالة الشيدرات: لا توجد شيدرات
    echo.
    echo        ┌──────────────────────────────────────┐
    echo        │                                      │
    echo        │    [1] 📥 إضافة شيدر                 │
    echo        │                                      │
    echo        │    [2] 🎮 فتح Minecraft              │
    echo        │                                      │
    echo        │    [3] 🚪 خروج                      │
    echo        │                                      │
    echo        └──────────────────────────────────────┘
    echo.
    echo        اختر رقم من 1 إلى 3:
    choice /c 123 /n >nul
    if errorlevel 3 exit
    if errorlevel 2 goto OPEN_GAME
    if errorlevel 1 goto ADD_SHADER
) else (
    echo        📁 حالة الشيدرات: يوجد %shader_count% شيدر
    echo.
    echo        ┌──────────────────────────────────────┐
    echo        │                                      │
    echo        │    [1] 📥 إضافة شيدر جديد            │
    echo        │                                      │
    echo        │    [2] ✅ تطبيق شيدر موجود           │
    echo        │                                      │
    echo        │    [3] 🎮 فتح Minecraft              │
    echo        │                                      │
    echo        │    [4] 🔄 إزالة جميع الشيدرات        │
    echo        │                                      │
    echo        │    [5] 🚪 خروج                      │
    echo        │                                      │
    echo        └──────────────────────────────────────┘
    echo.
    echo        اختر رقم من 1 إلى 5:
    choice /c 12345 /n >nul
    if errorlevel 5 exit
    if errorlevel 4 goto REMOVE_ALL
    if errorlevel 3 goto OPEN_GAME
    if errorlevel 2 goto APPLY_SHADER
    if errorlevel 1 goto ADD_SHADER
)

:ADD_SHADER
cls
echo.
echo        ████████████████████████████████████████
echo        █           📥 إضافة شيدر              █
echo        ████████████████████████████████████████
echo.
echo        📋 الخطوات:
echo.
echo        1️⃣  سيتم فتح مجلد الشيدرات
echo        2️⃣  ضع ملف الشيدر (.mcpack أو .zip) في المجلد
echo        3️⃣  اضغط أي زر بعد إضافة الشيدر
echo.
echo        🔄 جاري فتح المجلد...
start "" explorer "MCPACKS"
echo.
echo        ⏳ بعد إضافة الشيدر، اضغط أي زر للعودة...
pause >nul
goto MENU

:APPLY_SHADER
cls
echo.
echo        ████████████████████████████████████████
echo        █           ✅ تطبيق الشيدر            █
echo        ████████████████████████████████████████
echo.
echo        📦 الشيدرات المتاحة:
echo.

:: عرض قائمة الشيدرات
set counter=0
for %%f in ("MCPACKS\*.mcpack" "MCPACKS\*.zip") do (
    set /a counter+=1
    echo        [!counter!] %%~nxf
    set "shader_!counter!=%%f"
)

echo.
echo        [0] العودة للقائمة الرئيسية
echo.
set /p "selection=        اختر رقم الشيدر: "

if "%selection%"=="0" goto MENU
if %selection% gtr %counter% goto APPLY_SHADER
if %selection% lss 1 goto APPLY_SHADER

:: تطبيق الشيدر المختار
set "chosen_shader=!shader_%selection%!"
echo.
echo        ⏳ جاري تطبيق الشيدر...
echo        📁 الشيدر: !chosen_shader!
echo.

:: تنظيف المجلد
del "MATERIALS\*.material.bin" >nul 2>&1

:: إنشاء مجلد مؤقت
if exist "temp_extract" rmdir /s /q "temp_extract" >nul 2>&1
mkdir "temp_extract"

:: استخراج الشيدر
echo        📦 استخراج الملفات...
powershell -command "try { Expand-Archive -LiteralPath '!chosen_shader!' -DestinationPath 'temp_extract' -Force } catch { exit 1 }" >nul 2>&1

:: البحث عن ملفات المواد ونسخها
echo        🔍 البحث عن ملفات الشيدر...
set "found=0"

:: البحث في المجلد الرئيسي
if exist "temp_extract\renderer\materials\*.material.bin" (
    copy "temp_extract\renderer\materials\*.material.bin" "MATERIALS\" >nul 2>&1
    set "found=1"
    goto EXTRACTION_DONE
)

:: البحث في المجلدات الفرعية
for /d %%d in ("temp_extract\*") do (
    if exist "%%d\renderer\materials\*.material.bin" (
        copy "%%d\renderer\materials\*.material.bin" "MATERIALS\" >nul 2>&1
        set "found=1"
        goto EXTRACTION_DONE
    )
)

:: البحث في subpacks
for /d %%d in ("temp_extract\subpacks\*") do (
    if exist "%%d\renderer\materials\*.material.bin" (
        copy "%%d\renderer\materials\*.material.bin" "MATERIALS\" >nul 2>&1
        set "found=1"
        goto EXTRACTION_DONE
    )
)

:EXTRACTION_DONE
:: تنظيف المجلد المؤقت
rmdir /s /q "temp_extract" >nul 2>&1

if "%found%"=="0" (
    echo        ❌ خطأ: لم يتم العثور على ملفات الشيدر
    echo        💡 تأكد من أن الملف شيدر صحيح لـ Minecraft Bedrock
    echo.
    pause
    goto MENU
)

:: تطبيق الشيدر باستخدام Matject
echo        🚀 تطبيق الشيدر على Minecraft...
call matject.bat placebo >nul 2>&1

echo.
echo        ✅ تم تطبيق الشيدر بنجاح!
echo.
echo        🎮 هل تريد فتح Minecraft الآن؟
echo.
choice /c YN /n /m "        [Y] نعم    [N] لا: "
if errorlevel 2 goto MENU
if errorlevel 1 goto OPEN_GAME

:OPEN_GAME
cls
echo.
echo        ████████████████████████████████████████
echo        █           🎮 فتح Minecraft           █
echo        ████████████████████████████████████████
echo.
echo        🚀 جاري فتح Minecraft...
echo.

:: فتح Minecraft
start "" shell:AppsFolder\Microsoft.MinecraftUWP_8wekyb3d8bbwe!App >nul 2>&1

echo        ✅ تم فتح Minecraft بنجاح!
echo.
echo        📋 نصائح مهمة:
echo        • ادخل إلى إعدادات اللعبة
echo        • اذهب إلى "الموارد العامة" أو "Global Resource Packs"
echo        • فعل الشيدر المطلوب
echo        • أعد تشغيل العالم لرؤية التأثيرات
echo.
pause
goto MENU

:REMOVE_ALL
cls
echo.
echo        ████████████████████████████████████████
echo        █         🔄 إزالة الشيدرات           █
echo        ████████████████████████████████████████
echo.
echo        ⚠️  تحذير: هذا سيزيل جميع الشيدرات المطبقة
echo        ويستعيد مواد Minecraft الافتراضية
echo.
echo        هل أنت متأكد من المتابعة؟
echo.
choice /c YN /n /m "        [Y] نعم، أزل الشيدرات    [N] لا، ألغي العملية: "
if errorlevel 2 goto MENU

echo.
echo        🔄 جاري إزالة الشيدرات واستعادة الافتراضي...
del "MATERIALS\*.material.bin" >nul 2>&1
call matject.bat placebo >nul 2>&1
echo.
echo        ✅ تم استعادة مواد Minecraft الافتراضية
echo.
pause
goto MENU

MINECRAFT SHADER TOOLS - SIMPLE GUIDE
=====================================

FILES CREATED:
--------------
1. EasyShader.bat     - Super simple (recommended)
2. QuickShader.bat    - Quick and clean
3. ShaderTool.bat     - Full featured

HOW TO USE:
-----------
1. Put any of these files in the same folder as matject.bat
2. Run the file (right-click > Run as administrator)
3. Choose "Add Shader" and put your shader file in MCPACKS folder
4. Choose "Apply Shader" and select which shader to use
5. Choose "Open Minecraft" and enable shader in Global Resource Packs

REQUIREMENTS:
-------------
- Windows 10/11
- Minecraft Bedrock Edition
- Run as Administrator

FEATURES:
---------
- No IObit Unlocker needed
- Automatic setup
- Simple interface
- Direct Minecraft launch
- Safe and clean

TROUBLESHOOTING:
----------------
- If shader doesn't work: Make sure it's for Bedrock Edition
- If error appears: Run as Administrator
- If <PERSON><PERSON> won't open: Open manually from Start menu
- If no effects: Enable shader in game settings

SUPPORTED FORMATS:
------------------
- .mcpack files
- .zip files containing shaders

FOLDER STRUCTURE:
-----------------
matject-main/
├── matject.bat (original)
├── EasyShader.bat (new tool)
├── MCPACKS/ (put shaders here)
├── MATERIALS/ (temporary files)
└── .settings/ (auto-created)

SAFETY:
-------
- All tools are safe
- No internet connection needed
- Automatic backups created
- Easy to restore defaults

TIPS:
-----
- Close Minecraft before applying shaders
- Try different shaders to find your favorite
- Some shaders may affect performance
- You can switch shaders anytime

ENJOY YOUR SHADERS!

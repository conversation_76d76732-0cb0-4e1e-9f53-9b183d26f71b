@echo off
title Remove IObit Dependency - Clean Matject
color 0C

echo ================================================
echo     Remove IObit Unlocker Dependency
echo ================================================
echo.
echo This will modify Matject to work WITHOUT IObit Unlocker
echo.
echo What this does:
echo - Creates backup of original matject.bat
echo - Replaces IObit calls with direct file operations
echo - Enables Direct Write Mode permanently
echo - Removes all IObit-related checks
echo.
choice /c yn /n /m "Continue? [Y] Yes  [N] No: "
if errorlevel 2 exit

echo.
echo Processing...

:: Create backup
if not exist "matject_original.bat" (
    if exist "matject.bat" (
        copy "matject.bat" "matject_original.bat" >nul
        echo ✓ Backup created: matject_original.bat
    ) else (
        echo ✗ matject.bat not found!
        pause
        exit
    )
) else (
    echo ✓ Backup already exists
)

:: Create modified version
echo ✓ Creating IObit-free version...

(
echo @echo off
echo setlocal enabledelayedexpansion
echo title Matject - IObit Free Version
echo.
echo :: Check admin
echo net session ^>nul 2^>^&1
echo if %%errorlevel%% neq 0 ^(
echo     echo ERROR: Administrator privileges required
echo     echo Right-click and select "Run as administrator"
echo     pause
echo     exit
echo ^)
echo.
echo :: Get Minecraft location
echo for /f "tokens=*" %%%%i in ^('powershell -command "^(Get-AppxPackage -Name Microsoft.MinecraftUWP^).InstallLocation"'^) do set "MCLOCATION=%%%%i"
echo.
echo if "%%MCLOCATION%%"=="" ^(
echo     echo ERROR: Minecraft Bedrock not found!
echo     pause
echo     exit
echo ^)
echo.
echo set "MATERIALS_PATH=%%MCLOCATION%%\data\renderer\materials"
echo.
echo :: Setup directories
echo if not exist "MCPACKS" mkdir MCPACKS
echo if not exist "MATERIALS" mkdir MATERIALS  
echo if not exist "Backups" mkdir Backups
echo.
echo :: Create backup of original materials
echo if not exist "Backups\original_materials" ^(
echo     mkdir "Backups\original_materials"
echo     copy "%%MATERIALS_PATH%%\*.material.bin" "Backups\original_materials\" ^>nul 2^>^&1
echo ^)
echo.
echo :MENU
echo cls
echo echo Matject - IObit Free Version
echo echo ============================
echo echo.
echo echo Minecraft: %%MCLOCATION%%
echo echo.
echo echo [1] Apply Shader ^(Auto Method^)
echo echo [2] Apply Shader ^(Manual Method^)
echo echo [3] Restore Default Materials
echo echo [4] Open Minecraft
echo echo [5] Exit
echo echo.
echo choice /c 12345 /n
echo if errorlevel 5 exit
echo if errorlevel 4 goto OPEN_MINECRAFT
echo if errorlevel 3 goto RESTORE
echo if errorlevel 2 goto MANUAL
echo if errorlevel 1 goto AUTO
echo.
echo :AUTO
echo cls
echo echo Auto Method - Apply Shader from MCPACK
echo echo =====================================
echo echo.
echo echo Put your shader file ^(.mcpack or .zip^) in MCPACKS folder
echo start explorer MCPACKS
echo pause
echo.
echo :: Process MCPACK files
echo set /a count=0
echo for %%%%f in ^("MCPACKS\*.mcpack" "MCPACKS\*.zip"^) do set /a count+=1
echo.
echo if %%count%% equ 0 ^(
echo     echo No shader files found!
echo     pause
echo     goto MENU
echo ^)
echo.
echo :: List available shaders
echo set /a num=0
echo for %%%%f in ^("MCPACKS\*.mcpack" "MCPACKS\*.zip"^) do ^(
echo     set /a num+=1
echo     echo [^^!num^^!] %%%%~nxf
echo     set "shader^^!num^^!=%%%%f"
echo ^)
echo.
echo set /p choice="Choose shader number: "
echo.
echo :: Extract and apply selected shader
echo setlocal enabledelayedexpansion
echo set "selected_shader=^^!shader%%choice%%^^!"
echo.
echo if exist temp_extract rmdir /s /q temp_extract
echo mkdir temp_extract
echo.
echo powershell -command "Expand-Archive -LiteralPath '^^!selected_shader^^!' -DestinationPath 'temp_extract' -Force"
echo.
echo :: Find and copy materials
echo if exist "temp_extract\renderer\materials\*.material.bin" ^(
echo     copy "temp_extract\renderer\materials\*.material.bin" "%%MATERIALS_PATH%%\" ^>nul
echo     goto APPLIED
echo ^)
echo.
echo for /d %%%%d in ^("temp_extract\*"^) do ^(
echo     if exist "%%%%d\renderer\materials\*.material.bin" ^(
echo         copy "%%%%d\renderer\materials\*.material.bin" "%%MATERIALS_PATH%%\" ^>nul
echo         goto APPLIED
echo     ^)
echo ^)
echo.
echo for /d %%%%d in ^("temp_extract\subpacks\*"^) do ^(
echo     if exist "%%%%d\renderer\materials\*.material.bin" ^(
echo         copy "%%%%d\renderer\materials\*.material.bin" "%%MATERIALS_PATH%%\" ^>nul
echo         goto APPLIED
echo     ^)
echo ^)
echo.
echo echo ERROR: No shader materials found!
echo rmdir /s /q temp_extract
echo pause
echo goto MENU
echo.
echo :APPLIED
echo rmdir /s /q temp_extract
echo echo SUCCESS: Shader applied!
echo pause
echo goto MENU
echo.
echo :MANUAL
echo cls
echo echo Manual Method - Apply Materials from MATERIALS folder
echo echo ================================================
echo echo.
echo echo Put .material.bin files in MATERIALS folder
echo start explorer MATERIALS
echo pause
echo.
echo copy "MATERIALS\*.material.bin" "%%MATERIALS_PATH%%\" ^>nul
echo echo SUCCESS: Materials applied!
echo pause
echo goto MENU
echo.
echo :RESTORE
echo cls
echo echo Restore Default Materials
echo echo ========================
echo echo.
echo choice /c yn /n /m "Restore original materials? [Y] Yes  [N] No: "
echo if errorlevel 2 goto MENU
echo.
echo copy "Backups\original_materials\*.material.bin" "%%MATERIALS_PATH%%\" ^>nul
echo echo SUCCESS: Original materials restored!
echo pause
echo goto MENU
echo.
echo :OPEN_MINECRAFT
echo start shell:AppsFolder\Microsoft.MinecraftUWP_8wekyb3d8bbwe!App
echo echo Minecraft opened!
echo pause
echo goto MENU
) > "matject_no_iobit.bat"

echo ✓ Created: matject_no_iobit.bat

:: Clean up IObit-related files
if exist "fake_iobit" (
    rmdir /s /q "fake_iobit"
    echo ✓ Removed fake IObit folder
)

if exist ".settings\customIObitUnlockerPath.txt" (
    del ".settings\customIObitUnlockerPath.txt"
    echo ✓ Removed IObit path setting
)

echo.
echo ================================================
echo                 COMPLETED!
echo ================================================
echo.
echo Files created:
echo - matject_original.bat (backup)
echo - matject_no_iobit.bat (IObit-free version)
echo.
echo To use the new version:
echo 1. Run matject_no_iobit.bat as Administrator
echo 2. Use Auto Method for MCPACK files
echo 3. Use Manual Method for .material.bin files
echo.
echo The new version works completely independently
echo and does NOT require IObit Unlocker at all!
echo.
pause

# تحديث Matject - خيار اختيار طريقة التشغيل

## 🎯 ما الجديد؟

تم إضافة خيار في بداية تشغيل Matject للاختيار بين استخدام IObit Unlocker أو العمل بدونه (Direct Write Mode)، بالإضافة إلى زر سريع في اللوحة الرئيسية لتغيير الطريقة.

## 🚀 المميزات الجديدة

### 1. شاشة اختيار طريقة التشغيل
- تظهر عند أول تشغيل لـ Matject
- خيارات واضحة ومفصلة باللغة العربية
- معلومات مساعدة شاملة

### 2. طريقتان للتشغيل

#### الطريقة الأولى: IObit Unlocker (التقليدية)
- ✅ يتطلب تثبيت IObit Unlocker
- ✅ يعمل مع جميع إصدارات Minecraft
- ✅ الطريقة الأكثر استقراراً
- ✅ مناسب لمستخدمي Microsoft Store

#### الطريقة الثانية: Direct Write Mode (الجديدة)
- ✅ لا يتطلب تثبيت IObit Unlocker
- ✅ أسرع في تطبيق الشيدرات
- ✅ مناسب لمستخدمي Bedrock Launcher
- ✅ يعمل مع Minecraft المثبت خارج WindowsApps

### 3. إدارة ذكية للخيارات
- حفظ الاختيار تلقائياً
- إمكانية التغيير من الإعدادات
- عرض الطريقة المختارة في الشاشة الرئيسية

## 📋 كيفية الاستخدام

### التشغيل لأول مرة
1. شغل `matject.bat`
2. ستظهر شاشة اختيار طريقة التشغيل
3. اختر الطريقة المناسبة لك
4. اتبع التعليمات على الشاشة

### تغيير الطريقة لاحقاً
1. شغل Matject
2. اضغط `[S]` للإعدادات
3. اضغط `[I]` لتغيير طريقة IObit
4. اختر الطريقة الجديدة

## 🔧 التحسينات المضافة

### في الملف الرئيسي (matject.bat)
- شاشة اختيار أنيقة مع إطار
- رسائل باللغة العربية
- فحص ذكي لـ IObit Unlocker
- خيار التبديل السريع بين الطرق

### في الإعدادات (modules/settings.bat)
- خيار جديد "IObit mode" في الصفحة الأولى
- واجهة سهلة لتغيير الطريقة
- عرض الطريقة الحالية

### عرض المعلومات
- عرض الطريقة المختارة في الشاشة الرئيسية
- رسائل توضيحية عند التبديل
- معلومات مساعدة شاملة

## 🎨 التحسينات البصرية

- استخدام الألوان لتمييز الخيارات
- إطارات جميلة للشاشات المهمة
- رموز ✓ لتوضيح المميزات
- تنسيق أفضل للنصوص العربية

## 🔒 الأمان والاستقرار

- حفظ آمن للإعدادات
- عدم تعديل الملفات الأساسية
- إمكانية العودة للإعدادات الأصلية
- فحص صحة الملفات

## 📁 الملفات المعدلة

1. **matject.bat** - الملف الرئيسي
   - إضافة شاشة اختيار طريقة التشغيل
   - تحسين رسائل فحص IObit Unlocker
   - عرض الطريقة المختارة

2. **modules/settings.bat** - ملف الإعدادات
   - إضافة خيار "IObit mode"
   - واجهة تغيير الطريقة

## 🚨 ملاحظات مهمة

### متى تختار IObit Unlocker؟
- إذا كنت تستخدم Minecraft من Microsoft Store
- إذا كنت تريد الطريقة الأكثر استقراراً
- إذا لم تكن متأكداً من طريقة التثبيت

### متى تختار Direct Write Mode؟
- إذا كنت تستخدم Bedrock Launcher
- إذا كان Minecraft مثبت خارج WindowsApps
- إذا كنت تريد سرعة أكبر في التطبيق

## 🔄 الترقية من الإصدارات السابقة

إذا كنت تستخدم إصداراً سابقاً من Matject:
1. ستظهر شاشة اختيار الطريقة عند أول تشغيل
2. يمكنك الاختيار بناءً على استخدامك السابق
3. جميع إعداداتك السابقة ستبقى كما هي

## 🆘 استكشاف الأخطاء

### إذا لم تظهر شاشة الاختيار
- احذف ملف `.settings\iobitModeSelected.txt`
- شغل Matject مرة أخرى

### إذا لم يعمل Direct Write Mode
- تأكد من تشغيل Matject كمدير
- تحقق من أن Minecraft غير محمي
- جرب التبديل إلى IObit Unlocker

### إذا لم يعمل IObit Unlocker
- تأكد من تثبيت IObit Unlocker بشكل صحيح
- جرب تحديد المسار يدوياً من الإعدادات
- جرب التبديل إلى Direct Write Mode

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من دليل الاستخدام
2. راجع ملف `دليل_تشغيل_بدون_IObit.md`
3. تأكد من تشغيل Matject كمدير

---

**تم التطوير بواسطة:** فريق Matject  
**التاريخ:** 2025-01-30  
**الإصدار:** v3.6.0+

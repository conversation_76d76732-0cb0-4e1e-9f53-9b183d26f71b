@echo off
setlocal enabledelayedexpansion
title Matject - IObit Free Version

:: Check admin
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Administrator privileges required
    echo Right-click and select "Run as administrator"
    pause
    exit
)

:: Get Minecraft location
for /f "tokens=*" %%i in ('powershell -command "^(Get-AppxPackage -Name Microsoft.MinecraftUWP^).InstallLocation"') do set "MCLOCATION=%%i"

if "%MCLOCATION%"=="" (
    echo ERROR: Minecraft Bedrock not found!
    pause
    exit
)

set "MATERIALS_PATH=%MCLOCATION%\data\renderer\materials"

:: Setup directories
if not exist "MCPACKS" mkdir MCPACKS
if not exist "MATERIALS" mkdir MATERIALS  
if not exist "Backups" mkdir Backups

:: Create backup of original materials
if not exist "Backups\original_materials" (
    mkdir "Backups\original_materials"
    copy "%MATERIALS_PATH%\*.material.bin" "Backups\original_materials\" >nul 2>&1
)

:MENU
cls
echo Matject - IObit Free Version
echo ============================
echo.
echo Minecraft: %MCLOCATION%
echo.
echo [1] Apply Shader (Auto Method)
echo [2] Apply Shader (Manual Method)
echo [3] Restore Default Materials
echo [4] Open Minecraft
echo [5] Exit
echo.
choice /c 12345 /n
if errorlevel 5 exit
if errorlevel 4 goto OPEN_MINECRAFT
if errorlevel 3 goto RESTORE
if errorlevel 2 goto MANUAL
if errorlevel 1 goto AUTO

:AUTO
cls
echo Auto Method - Apply Shader from MCPACK
echo =====================================
echo.
echo Put your shader file (.mcpack or .zip) in MCPACKS folder
start explorer MCPACKS
pause

:: Process MCPACK files
set /a count=0
for %%f in ("MCPACKS\*.mcpack" "MCPACKS\*.zip") do set /a count+=1

if %count% equ 0 (
    echo No shader files found!
    pause
    goto MENU
)

:: List available shaders
set /a num=0
for %%f in ("MCPACKS\*.mcpack" "MCPACKS\*.zip") do (
    set /a num+=1
    echo [^!num^!] %%~nxf
    set "shader^^!num^^!=%%f"
)

set /p choice="Choose shader number: "

:: Extract and apply selected shader
setlocal enabledelayedexpansion
set "selected_shader=^^!shader%choice%^^!"

if exist temp_extract rmdir /s /q temp_extract
mkdir temp_extract

powershell -command "Expand-Archive -LiteralPath '^^!selected_shader^^!' -DestinationPath 'temp_extract' -Force"

:: Find and copy materials
if exist "temp_extract\renderer\materials\*.material.bin" (
    copy "temp_extract\renderer\materials\*.material.bin" "%MATERIALS_PATH%\" >nul
    goto APPLIED
)

for /d %%d in ("temp_extract\*") do (
    if exist "%%d\renderer\materials\*.material.bin" (
        copy "%%d\renderer\materials\*.material.bin" "%MATERIALS_PATH%\" >nul
        goto APPLIED
    )
)

for /d %%d in ("temp_extract\subpacks\*") do (
    if exist "%%d\renderer\materials\*.material.bin" (
        copy "%%d\renderer\materials\*.material.bin" "%MATERIALS_PATH%\" >nul
        goto APPLIED
    )
)

echo ERROR: No shader materials found!
rmdir /s /q temp_extract
pause
goto MENU

:APPLIED
rmdir /s /q temp_extract
echo SUCCESS: Shader applied!
pause
goto MENU

:MANUAL
cls
echo Manual Method - Apply Materials from MATERIALS folder
echo ================================================
echo.
echo Put .material.bin files in MATERIALS folder
start explorer MATERIALS
pause

copy "MATERIALS\*.material.bin" "%MATERIALS_PATH%\" >nul
echo SUCCESS: Materials applied!
pause
goto MENU

:RESTORE
cls
echo Restore Default Materials
echo ========================
echo.
choice /c yn /n /m "Restore original materials? [Y] Yes  [N] No: "
if errorlevel 2 goto MENU

copy "Backups\original_materials\*.material.bin" "%MATERIALS_PATH%\" >nul
echo SUCCESS: Original materials restored!
pause
goto MENU

:OPEN_MINECRAFT
start shell:AppsFolder\Microsoft.MinecraftUWP_8wekyb3d8bbwe!App
echo Minecraft opened!
pause
goto MENU

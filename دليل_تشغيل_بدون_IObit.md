# دليل تشغيل Matject بدون IObit Unlocker

## 🎯 الهدف
تشغيل أداة Matject لتطبيق الشيدرات على Minecraft Bedrock بدون الحاجة لتثبيت IObit Unlocker.

## 📋 المتطلبات
- Windows 10/11
- Minecraft Bedrock Edition
- صلاحيات المدير (Administrator)

## 🔧 الطرق المتاحة

### الطريقة الأولى: تفعيل Direct Write Mode (الأسهل)

1. **شغل الملف**: `enable_direct_write_mode.bat`
2. **شغل Matject**: `matject.bat`
3. **استخدم الأداة بشكل طبيعي**

### الطريقة الثانية: تجاوز فحص IObit Unlocker (الأشمل)

1. **شغل الملف**: `bypass_iobit_check.bat`
2. **شغل Matject**: `matject.bat`
3. **لن تحتاج لتثبيت IObit Unlocker**

### الطريقة الثالثة: التفعيل اليدوي

1. شغل `matject.bat`
2. اضغط `[S]` للإعدادات
3. اضغط `[D]` أو `[4]` للانتقال لتبويب "Updates & Debug"
4. اضغط `[W]` للانتقال للصفحة الثانية
5. اضغط `[5]` لتفعيل "Direct write mode"
6. اضغط `[Y]` للموافقة على الاختبار

## ⚠️ ملاحظات مهمة

### متى يعمل Direct Write Mode:
- ✅ عند استخدام Bedrock Launcher
- ✅ عند تثبيت Minecraft خارج مجلد WindowsApps
- ✅ عند وجود صلاحيات كتابة في مجلد Minecraft

### متى لا يعمل:
- ❌ Minecraft مثبت في WindowsApps ومحمي
- ❌ عدم وجود صلاحيات مدير
- ❌ مجلد Minecraft محمي بواسطة النظام

## 🔄 استعادة الإعدادات الأصلية

إذا أردت العودة للإعدادات الأصلية:
```
شغل: restore_original.bat
```

## 🛠️ استكشاف الأخطاء

### إذا لم يعمل Direct Write Mode:
1. تأكد من تشغيل Matject كمدير
2. تحقق من أن Minecraft غير محمي
3. جرب استخدام Bedrock Launcher بدلاً من Microsoft Store

### إذا ظهرت رسائل خطأ:
1. تأكد من إغلاق Minecraft تماماً
2. شغل Command Prompt كمدير
3. انتقل لمجلد Matject وشغل الأداة

## 📁 الملفات المنشأة

- `enable_direct_write_mode.bat` - تفعيل Direct Write Mode
- `bypass_iobit_check.bat` - تجاوز فحص IObit Unlocker
- `restore_original.bat` - استعادة الإعدادات الأصلية
- `.settings\directWriteMode.txt` - ملف تفعيل Direct Write Mode
- `.settings\customIObitUnlockerPath.txt` - مسار IObit Unlocker المخصص

## ✅ التحقق من نجاح العملية

عند تشغيل Matject بنجاح بدون IObit Unlocker، ستظهر رسالة:
```
[Direct write mode]
```

## 🔒 الأمان

جميع التعديلات آمنة ولا تؤثر على:
- ملفات النظام
- إعدادات Windows
- ملفات Minecraft الأساسية

الأداة تنشئ نسخ احتياطية تلقائياً قبل أي تعديل.

@echo off
setlocal enabledelayedexpansion
title Matject - اختيار طريقة التشغيل
cls

:: Check if running from temp folder
echo %cd% | findstr /C:"Local\Temp" /I >nul && (
    title EXTRACT FIRST!
    cls
    echo [93m[!] هل تحاول تشغيل Matject بدون استخراج؟
    echo     يجب استخراجه أولاً![0m
    echo.
    echo جاري الخروج...[?25h
    echo.
    pause
    exit
)

:: Check if matject.bat exists
if not exist "%cd%\matject.bat" (
    title FOLDER NOT FOUND!
    cls
    echo [93m[!] لا يمكن العثور على مجلد Matject تلقائياً[0m
    echo.
    echo جاري الخروج...[?25h
    echo.
    pause
    exit
)

:: Check for existing mode selection
if exist ".settings\iobitModeSelected.txt" (
    set /p selectedMode=<".settings\iobitModeSelected.txt"
    if "!selectedMode!"=="iobit" (
        echo [92m[*] الطريقة المحددة: IObit Unlocker[0m
        echo [93m[*] جاري تشغيل Matject...[0m
        timeout 2 >nul
        start /b cmd /k "matject.bat"
        exit
    ) else if "!selectedMode!"=="direct" (
        echo [92m[*] الطريقة المحددة: Direct Write Mode[0m
        echo [93m[*] جاري تشغيل Matject...[0m
        timeout 2 >nul
        start /b cmd /k "matject.bat"
        exit
    )
)

:: Main selection menu
:MAIN_MENU
cls
echo [97m╔══════════════════════════════════════════════════════════════╗[0m
echo [97m║                    [96mMatject - أداة الشيدرات[97m                    ║[0m
echo [97m║                  [93mاختيار طريقة التشغيل[97m                      ║[0m
echo [97m╚══════════════════════════════════════════════════════════════╝[0m
echo.
echo [93m[?] اختر طريقة التشغيل المناسبة لك:[0m
echo.
echo [92m[1] استخدام IObit Unlocker [90m(الطريقة التقليدية)[0m
echo     ✓ يتطلب تثبيت IObit Unlocker
echo     ✓ يعمل مع جميع إصدارات Minecraft
echo     ✓ الطريقة الأكثر استقراراً
echo     ✓ مناسب لمستخدمي Microsoft Store
echo.
echo [94m[2] التشغيل بدون IObit Unlocker [90m(Direct Write Mode)[0m
echo     ✓ لا يتطلب تثبيت IObit Unlocker
echo     ✓ أسرع في تطبيق الشيدرات
echo     ✓ مناسب لمستخدمي Bedrock Launcher
echo     ✓ يعمل مع Minecraft المثبت خارج WindowsApps
echo.
echo [96m[3] معلومات ومساعدة إضافية[0m
echo [91m[4] خروج[0m
echo.
echo [93mاختر الرقم المناسب...[0m
echo.
choice /c 1234 /n >nul

if !errorlevel! equ 1 goto IOBIT_MODE
if !errorlevel! equ 2 goto DIRECT_MODE
if !errorlevel! equ 3 goto HELP_INFO
if !errorlevel! equ 4 exit

:IOBIT_MODE
cls
echo [92m[*] تم اختيار: استخدام IObit Unlocker[0m
echo.

:: Create settings folder if not exists
if not exist ".settings" mkdir .settings

:: Save selection
echo iobit>".settings\iobitModeSelected.txt"

echo [93m[*] جاري فحص وجود IObit Unlocker...[0m
echo.

:: Check if IObit Unlocker is installed
if not exist "%ProgramFiles(x86)%\IObit\IObit Unlocker\IObitUnlocker.exe" (
    echo [91m[!] لم يتم العثور على IObit Unlocker![0m
    echo.
    echo [93m[?] هل تريد تحميله الآن؟[0m
    echo.
    echo [92m[Y] نعم، افتح الموقع الرسمي[0m
    echo [91m[N] لا، سأقوم بتحميله لاحقاً[0m
    echo [94m[C] تغيير الطريقة إلى Direct Write Mode[0m
    echo.
    choice /c ync /n >nul
    
    if !errorlevel! equ 1 (
        start https://www.iobit.com/en/iobit-unlocker.php
        echo [93m[*] تم فتح الموقع الرسمي لتحميل IObit Unlocker[0m
        echo [93m[*] بعد التثبيت، شغل هذا الملف مرة أخرى[0m
        pause
        exit
    )
    if !errorlevel! equ 2 (
        echo [93m[*] يمكنك تشغيل هذا الملف مرة أخرى بعد تثبيت IObit Unlocker[0m
        pause
        exit
    )
    if !errorlevel! equ 3 goto DIRECT_MODE
) else (
    echo [92m[*] تم العثور على IObit Unlocker بنجاح![0m
    echo [93m[*] جاري تشغيل Matject...[0m
    timeout 2 >nul
    start /b cmd /k "matject.bat"
    exit
)

:DIRECT_MODE
cls
echo [94m[*] تم اختيار: التشغيل بدون IObit Unlocker[0m
echo.

:: Create settings folder if not exists
if not exist ".settings" mkdir .settings

:: Save selection and enable direct write mode
echo direct>".settings\iobitModeSelected.txt"
echo being outside WindowsApps feels like freedom. [%date% // %time:~0,-6%]>".settings\directWriteMode.txt"

echo [92m[*] تم تفعيل Direct Write Mode بنجاح![0m
echo [93m[*] جاري تشغيل Matject...[0m
timeout 2 >nul
start /b cmd /k "matject.bat"
exit

:HELP_INFO
cls
echo [97m╔══════════════════════════════════════════════════════════════╗[0m
echo [97m║                    [96mمعلومات ومساعدة[97m                        ║[0m
echo [97m╚══════════════════════════════════════════════════════════════╝[0m
echo.
echo [92m■ الطريقة الأولى - IObit Unlocker:[0m
echo   • تحتاج لتحميل وتثبيت IObit Unlocker من الموقع الرسمي
echo   • تعمل مع جميع إصدارات Minecraft بدون استثناء
echo   • الطريقة الأكثر استقراراً والأقل مشاكل
echo   • تحتاج صلاحيات مدير عند أول استخدام
echo   • مناسبة لمستخدمي Microsoft Store
echo.
echo [94m■ الطريقة الثانية - Direct Write Mode:[0m
echo   • لا تحتاج لتثبيت أي برامج إضافية
echo   • أسرع في تطبيق الشيدرات
echo   • تعمل مع Bedrock Launcher أو Minecraft المثبت خارج WindowsApps
echo   • قد لا تعمل مع بعض إصدارات Minecraft من Microsoft Store
echo   • تحتاج صلاحيات مدير
echo.
echo [93m■ التوصيات:[0m
echo   • إذا كنت تستخدم Bedrock Launcher: اختر الطريقة الثانية
echo   • إذا كنت تستخدم Microsoft Store: اختر الطريقة الأولى
echo   • إذا لم تكن متأكداً: اختر الطريقة الأولى
echo.
echo [91m■ ملاحظات مهمة:[0m
echo   • يمكنك تغيير الطريقة لاحقاً من إعدادات Matject
echo   • جميع التعديلات آمنة ولا تؤثر على ملفات النظام
echo   • الأداة تنشئ نسخ احتياطية تلقائياً قبل أي تعديل
echo.
echo [96m[B] العودة للقائمة الرئيسية[0m
echo [91m[E] خروج[0m
echo.
choice /c be /n >nul

if !errorlevel! equ 1 goto MAIN_MENU
if !errorlevel! equ 2 exit

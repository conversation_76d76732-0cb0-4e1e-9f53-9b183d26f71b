@echo off
setlocal enabledelayedexpansion

echo ========================================
echo Matject IObit Mode Diagnosis
echo ========================================
echo.

echo 1. Checking if .settings folder exists:
if exist ".settings" (
    echo    ✓ .settings folder exists
) else (
    echo    ✗ .settings folder does not exist
)

echo.
echo 2. Checking if iobitModeSelected.txt exists:
if exist ".settings\iobitModeSelected.txt" (
    echo    ✓ iobitModeSelected.txt exists
    echo    Content:
    type ".settings\iobitModeSelected.txt"
) else (
    echo    ✗ iobitModeSelected.txt does not exist
)

echo.
echo 3. Checking if directWriteMode.txt exists:
if exist ".settings\directWriteMode.txt" (
    echo    ✓ directWriteMode.txt exists
) else (
    echo    ✗ directWriteMode.txt does not exist
)

echo.
echo 4. Testing variable assignment:
if exist ".settings\iobitModeSelected.txt" (
    set /p currentIObitMode=<".settings\iobitModeSelected.txt"
    echo    Read mode: [!currentIObitMode!]
    if "!currentIObitMode!"=="iobit" (
        set "iobitModeDisplay=IObit Unlocker"
        echo    Set display to: IObit Unlocker
    ) else if "!currentIObitMode!"=="direct" (
        set "iobitModeDisplay=Direct Write Mode"
        echo    Set display to: Direct Write Mode
    ) else (
        set "iobitModeDisplay=Unknown Mode"
        echo    Set display to: Unknown Mode
    )
) else (
    set "iobitModeDisplay=Not Selected"
    echo    Set display to: Not Selected
)

echo.
echo 5. Final display value:
echo    [!iobitModeDisplay!]

echo.
echo 6. Testing the actual line that should appear:
echo    [I] IObit Mode: !iobitModeDisplay!

echo.
echo ========================================
echo Diagnosis complete
echo ========================================
pause

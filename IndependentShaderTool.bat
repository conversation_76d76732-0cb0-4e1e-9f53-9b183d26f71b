@echo off
setlocal enabledelayedexpansion
title Independent Shader Tool - No IObit Required
color 0A

:: Check admin privileges
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: This tool requires Administrator privileges
    echo Right-click and select "Run as administrator"
    pause
    exit
)

:: Setup directories
if not exist "MCPACKS" mkdir MCPACKS
if not exist "MATERIALS" mkdir MATERIALS
if not exist "Backups" mkdir Backups

:: Get Minecraft location
for /f "tokens=*" %%i in ('powershell -command "(Get-AppxPackage -Name Microsoft.MinecraftUWP).InstallLocation"') do set "MINECRAFT_PATH=%%i"

if "%MINECRAFT_PATH%"=="" (
    echo ERROR: Minecraft Bedrock not found!
    echo Please install Minecraft Bedrock from Microsoft Store
    pause
    exit
)

set "MATERIALS_PATH=%MINECRAFT_PATH%\data\renderer\materials"

echo Minecraft found at: %MINECRAFT_PATH%
echo.

:MENU
cls
echo.
echo ================================================
echo     Independent Shader Tool (No IObit)
echo ================================================
echo.

set /a count=0
for %%f in ("MCPACKS\*.mcpack" "MCPACKS\*.zip") do set /a count+=1

echo Minecraft: %MINECRAFT_PATH%
echo Shaders available: %count%
echo.
echo [1] Add Shader
if %count% gtr 0 echo [2] Apply Shader
echo [3] Restore Default
echo [4] Open Minecraft
echo [5] Exit
echo.

if %count% gtr 0 (
    choice /c 12345 /n /m "Choose option: "
    if errorlevel 5 exit
    if errorlevel 4 goto OPEN_GAME
    if errorlevel 3 goto RESTORE
    if errorlevel 2 goto APPLY
    if errorlevel 1 goto ADD
) else (
    choice /c 1345 /n /m "Choose option: "
    if errorlevel 4 exit
    if errorlevel 3 goto OPEN_GAME
    if errorlevel 2 goto RESTORE
    if errorlevel 1 goto ADD
)

:ADD
cls
echo.
echo ================================================
echo              Add New Shader
echo ================================================
echo.
echo Opening MCPACKS folder...
echo Put your shader file (.mcpack or .zip) there
echo Then press any key to continue...
echo.
start "" explorer "MCPACKS"
pause >nul
goto MENU

:APPLY
cls
echo.
echo ================================================
echo              Apply Shader
echo ================================================
echo.

:: Create backup first
if not exist "Backups\original_materials" (
    echo Creating backup of original materials...
    mkdir "Backups\original_materials"
    copy "%MATERIALS_PATH%\*.material.bin" "Backups\original_materials\" >nul 2>&1
    echo Backup created successfully!
    echo.
)

echo Available shaders:
echo.

set /a num=0
for %%f in ("MCPACKS\*.mcpack" "MCPACKS\*.zip") do (
    set /a num+=1
    echo [!num!] %%~nxf
    set "shader!num!=%%f"
)

echo.
echo [0] Back to menu
set /p choice="Choose shader number: "

if "%choice%"=="0" goto MENU
if %choice% gtr %num% goto APPLY
if %choice% lss 1 goto APPLY

set "selected_shader=!shader%choice%!"
echo.
echo Applying shader: !selected_shader!
echo.

:: Clean temp folder
if exist "temp_shader" rmdir /s /q "temp_shader"
mkdir "temp_shader"

:: Extract shader
echo Extracting shader files...
powershell -command "try { Expand-Archive -LiteralPath '!selected_shader!' -DestinationPath 'temp_shader' -Force } catch { Write-Host 'Extraction failed'; exit 1 }"

if %errorlevel% neq 0 (
    echo ERROR: Failed to extract shader file
    rmdir /s /q "temp_shader"
    pause
    goto MENU
)

:: Find material files
echo Looking for material files...
set "materials_found=0"

if exist "temp_shader\renderer\materials\*.material.bin" (
    set "source_path=temp_shader\renderer\materials"
    set "materials_found=1"
    goto COPY_MATERIALS
)

for /d %%d in ("temp_shader\*") do (
    if exist "%%d\renderer\materials\*.material.bin" (
        set "source_path=%%d\renderer\materials"
        set "materials_found=1"
        goto COPY_MATERIALS
    )
)

for /d %%d in ("temp_shader\subpacks\*") do (
    if exist "%%d\renderer\materials\*.material.bin" (
        set "source_path=%%d\renderer\materials"
        set "materials_found=1"
        goto COPY_MATERIALS
    )
)

:COPY_MATERIALS
if "%materials_found%"=="0" (
    echo ERROR: No material files found in shader!
    echo This might not be a valid Minecraft Bedrock shader.
    rmdir /s /q "temp_shader"
    pause
    goto MENU
)

:: Copy materials to Minecraft
echo Copying shader materials to Minecraft...
copy "!source_path!\*.material.bin" "%MATERIALS_PATH%\" >nul 2>&1

if %errorlevel% neq 0 (
    echo ERROR: Failed to copy materials to Minecraft folder
    echo Make sure Minecraft is closed and you're running as Administrator
    rmdir /s /q "temp_shader"
    pause
    goto MENU
)

:: Clean up
rmdir /s /q "temp_shader"

echo.
echo SUCCESS: Shader applied successfully!
echo.
echo IMPORTANT: 
echo 1. Open Minecraft
echo 2. Go to Settings > Global Resource Packs
echo 3. Enable your shader pack
echo 4. Apply and restart the world
echo.
echo Open Minecraft now?
choice /c yn /n /m "[Y] Yes  [N] No: "
if errorlevel 2 goto MENU
if errorlevel 1 goto OPEN_GAME

:RESTORE
cls
echo.
echo ================================================
echo           Restore Default Materials
echo ================================================
echo.

if not exist "Backups\original_materials\*.material.bin" (
    echo ERROR: No backup found!
    echo Cannot restore original materials.
    pause
    goto MENU
)

echo This will restore original Minecraft materials
echo and remove all shader effects.
echo.
choice /c yn /n /m "Continue? [Y] Yes  [N] No: "
if errorlevel 2 goto MENU

echo.
echo Restoring original materials...
copy "Backups\original_materials\*.material.bin" "%MATERIALS_PATH%\" >nul 2>&1

if %errorlevel% neq 0 (
    echo ERROR: Failed to restore materials
    echo Make sure Minecraft is closed and you're running as Administrator
    pause
    goto MENU
)

echo.
echo SUCCESS: Original materials restored!
echo Shader effects have been removed.
pause
goto MENU

:OPEN_GAME
echo.
echo Opening Minecraft...
start "" shell:AppsFolder\Microsoft.MinecraftUWP_8wekyb3d8bbwe!App >nul 2>&1
echo.
echo Minecraft should be opening now.
echo Don't forget to enable your shader in Global Resource Packs!
pause
goto MENU

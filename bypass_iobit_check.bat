@echo off
echo تعديل Matject لتجاوز فحص IObit Unlocker...
echo.

:: إنشاء نسخة احتياطية من الملف الأصلي
if not exist "matject_backup.bat" (
    copy "matject.bat" "matject_backup.bat" >nul
    echo ✅ تم إنشاء نسخة احتياطية: matject_backup.bat
)

:: إنشاء مجلد الإعدادات
if not exist ".settings" mkdir .settings

:: تفعيل Direct Write Mode
echo being outside WindowsApps feels like freedom. [%date% // %time:~0,-6%]>".settings\directWriteMode.txt"

:: إنشاء ملف وهمي لـ IObit Unlocker لتجاوز الفحص
if not exist "fake_iobit" mkdir fake_iobit
echo @echo off > "fake_iobit\IObitUnlocker.exe"
echo echo IObit Unlocker تم تجاوزه - استخدام Direct Write Mode >> "fake_iobit\IObitUnlocker.exe"
echo exit /b 0 >> "fake_iobit\IObitUnlocker.exe"

:: إنشاء ملف DLL وهمي
echo. > "fake_iobit\IObitUnlocker.dll"

:: إنشاء ملف إعدادات مسار IObit Unlocker المخصص
echo %cd%\fake_iobit>".settings\customIObitUnlockerPath.txt"

echo ✅ تم تعديل Matject بنجاح!
echo.
echo التعديلات المطبقة:
echo - تفعيل Direct Write Mode
echo - إنشاء IObit Unlocker وهمي لتجاوز الفحص
echo - تعيين مسار مخصص لـ IObit Unlocker
echo.
echo الآن يمكنك تشغيل Matject بدون الحاجة لتثبيت IObit Unlocker
echo.
echo لاستعادة الإعدادات الأصلية، شغل: restore_original.bat
echo.
pause

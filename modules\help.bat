:: help.bat // Made by github.com/faizul726
@echo off
if not defined murgi echo [41;97mYou're supposed to open matject.bat, NOT ME.[0m :P[?25h & echo on & @cmd /k

title %title% Help
cls
echo !GRN![TIP]!RST! Check !CYN!faizul726.github.io/matject!RST! for better guide.
echo.
echo !YLW!Q. How to use it?!RST!
echo A. This is a text-based user interface.
echo    Means you have to navigate using keyboard buttons.
echo    All actions are labelled with the key to press.
echo    Everything is self explanatory. So, just explore by yourself.
echo.
echo !YLW!Q. Do I need IObit Unlocker to be installed?!RST!
echo A. Yes.
echo.
echo !YLW!Q. Do I need to disable anti-virus?!RST!
echo A. Yes, especially ransomware protection as it might make <PERSON><PERSON> unable to read/write files.
echo    If you don't want to then you can allow full access to folders that are mentioned in FAQ of README.md
echo.
echo !YLW!Q. Does it work on all version?!RST!
echo A. Maybe.
echo.
echo !YLW!Q. It's not working/crashing, what to do?!RST!
echo A. Join !CYN!faizul726.github.io/newb-discord!RST! and send message in #newb-support.
echo    I will help.
echo. 
echo !YLW!Q. What is matjectNEXT?!RST!
echo    An advanced variant of Matject that works somewhat like patched Minecraft.
echo    ^(Global Resource Packs ONLY^)
%backmsg%

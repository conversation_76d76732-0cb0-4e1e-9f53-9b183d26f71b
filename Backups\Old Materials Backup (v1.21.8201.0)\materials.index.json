{"materials": [{"name": "Actor", "path": "Actor"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "ActorBannerForwardPBR", "path": "ActorBannerForwardPBR"}, {"name": "ActorBannerPrepass", "path": "ActorBannerPrepass"}, {"name": "ActorBase", "path": "ActorBase"}, {"name": "ActorForwardPBR", "path": "ActorForwardPBR"}, {"name": "<PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON>"}, {"name": "ActorGlintForwardPBR", "path": "ActorGlintForwardPBR"}, {"name": "ActorGlintPrepass", "path": "ActorGlintPrepass"}, {"name": "ActorMultiTexture", "path": "ActorMultiTexture"}, {"name": "ActorMultiTextureForwardPBR", "path": "ActorMultiTextureForwardPBR"}, {"name": "ActorMultiTexturePrepass", "path": "ActorMultiTexturePrepass"}, {"name": "ActorPattern", "path": "ActorPattern"}, {"name": "ActorPatternForwardPBR", "path": "ActorPatternForwardPBR"}, {"name": "ActorPatternGlint", "path": "ActorPatternGlint"}, {"name": "ActorPatternGlintForwardPBR", "path": "ActorPatternGlintForwardPBR"}, {"name": "ActorPatternGlintPrepass", "path": "ActorPatternGlintPrepass"}, {"name": "ActorPatternPrepass", "path": "ActorPatternPrepass"}, {"name": "ActorPrepass", "path": "ActorPrepass"}, {"name": "Actor<PERSON><PERSON>", "path": "Actor<PERSON><PERSON>"}, {"name": "ActorTintForwardPBR", "path": "ActorTintForwardPBR"}, {"name": "ActorTintPrepass", "path": "ActorTintPrepass"}, {"name": "PBR/BRDF", "path": "BRDF"}, {"name": "PBR/Opaque", "path": "Basic"}, {"name": "BeaconBeam", "path": "BeaconBeam"}, {"name": "BilateralGrid", "path": "BilateralGrid"}, {"name": "Blit", "path": "Blit"}, {"name": "BlitColor", "path": "BlitColor"}, {"name": "BlitHDR", "path": "BlitHDR"}, {"name": "BlockOutlineSelection/Base", "path": "BlockOutlineSelection"}, {"name": "BlockSelectionOutline", "path": "BlockSelectionOutline"}, {"name": "BlockSelectionOverlay", "path": "BlockSelectionOverlay"}, {"name": "BlockSelectionOverlayBlockEntity", "path": "BlockSelectionOverlayBlockEntity"}, {"name": "Bloom", "path": "Bloom"}, {"name": "Core/Builtins", "path": "Builtins"}, {"name": "CameraAimAssistHighlight", "path": "CameraAimAssistHighlight"}, {"name": "Checkerboarding", "path": "Checkerboarding"}, {"name": "ClearVolume", "path": "ClearVolume"}, {"name": "Clouds", "path": "Clouds"}, {"name": "CloudsForwardPBR", "path": "CloudsForwardPBR"}, {"name": "Core/Color", "path": "Color"}, {"name": "ColorPostProcessing", "path": "ColorPostProcessing"}, {"name": "Cracks", "path": "Cracks"}, {"name": "CracksBlockEntity", "path": "CracksBlockEntity"}, {"name": "Cubemap", "path": "Cubemap"}, {"name": "CubemapHDRi", "path": "CubemapHDRi"}, {"name": "CubemapTransfer", "path": "CubemapTransfer"}, {"name": "DeferredIndirectSpecular", "path": "DeferredIndirectSpecular"}, {"name": "DeferredMixedResolution", "path": "DeferredMixedResolution"}, {"name": "DeferredMultipass", "path": "DeferredMultipass"}, {"name": "DeferredShading", "path": "DeferredShading"}, {"name": "DeferredShadingBase", "path": "DeferredShadingBase"}, {"name": "DeferredTiledMixedRes", "path": "DeferredTiledMixedRes"}, {"name": "DeferredWater", "path": "DeferredWater"}, {"name": "EditorBlockVolumeHull", "path": "EditorBlockVolumeHull"}, {"name": "EditorBlockVolumeWireframe", "path": "EditorBlockVolumeWireframe"}, {"name": "EditorHighlight", "path": "EditorHighlight"}, {"name": "EditorSelectionCursor", "path": "EditorSelectionCursor"}, {"name": "EndPortal", "path": "EndPortal"}, {"name": "EndPortalForwardPBR", "path": "EndPortalForwardPBR"}, {"name": "EndSky", "path": "EndSky"}, {"name": "EndSkyForwardPBR", "path": "EndSkyForwardPBR"}, {"name": "Entity", "path": "Entity"}, {"name": "FSR1", "path": "FSR1"}, {"name": "FlameBillboard", "path": "FlameBillboard"}, {"name": "Flipbook", "path": "Flipbook"}, {"name": "FloatingPointClear", "path": "FloatingPointClear"}, {"name": "FullscreenEffect", "path": "FullscreenEffect"}, {"name": "GPUDebugUtils", "path": "GPUDebugUtils"}, {"name": "Gameface/ClearQuad", "path": "GamefaceClearQuad"}, {"name": "Gameface/Path", "path": "GamefacePath"}, {"name": "Gameface/Renoir", "path": "GamefaceRenoir"}, {"name": "Gameface/Standard", "path": "GamefaceStandard"}, {"name": "Gameface/StandardBatched", "path": "GamefaceStandardBatched"}, {"name": "Gameface/StandardRare", "path": "GamefaceStandardRare"}, {"name": "Gameface/Stencil", "path": "GamefaceStencil"}, {"name": "Gameface/StencilPath", "path": "GamefaceStencilPath"}, {"name": "Gameface/StencilRare", "path": "GamefaceStencilRare"}, {"name": "Gameface/TexturesWithColorMix", "path": "GamefaceTexturesWithColorMix"}, {"name": "GammaScreenColorPostProcessing", "path": "GammaScreenColorPostProcessing"}, {"name": "GlowInkSignForwardPBR", "path": "GlowInkSignForwardPBR"}, {"name": "HiZAmbientDownSample", "path": "HiZAmbientDownSample"}, {"name": "HiZDownSample", "path": "HiZDownSample"}, {"name": "Debug/ImGui", "path": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "ItemInHandColor", "path": "ItemInHandColor"}, {"name": "ItemInHandColorGlint", "path": "ItemInHandColorGlint"}, {"name": "ItemInHandForwardPBR", "path": "ItemInHandForwardPBR"}, {"name": "ItemInHandForwardPBRGlint", "path": "ItemInHandForwardPBRGlint"}, {"name": "ItemInHandForwardPBRTextured", "path": "ItemInHandForwardPBRTextured"}, {"name": "ItemInHandPrepass", "path": "ItemInHandPrepass"}, {"name": "ItemInHandPrepassGlint", "path": "ItemInHandPrepassGlint"}, {"name": "ItemInHandPrepassTextured", "path": "ItemInHandPrepassTextured"}, {"name": "ItemInHandTextured", "path": "ItemInHandTextured"}, {"name": "LegacyCubemap", "path": "LegacyCubemap"}, {"name": "LegacyCubemapForwardPBR", "path": "LegacyCubemapForwardPBR"}, {"name": "LightClustering", "path": "LightClustering"}, {"name": "LocalExposure", "path": "LocalExposure"}, {"name": "LuminanceHistogram", "path": "LuminanceHistogram"}, {"name": "MeshFallbackForwardPBR", "path": "MeshFallbackForwardPBR"}, {"name": "MeshFallbackMovingBlockForwardPBR", "path": "MeshFallbackMovingBlockForwardPBR"}, {"name": "MeshFallbackMovingBlockPrepass", "path": "MeshFallbackMovingBlockPrepass"}, {"name": "MeshFallbackPosUVNormalColor", "path": "MeshFallbackPosUVNormalColor"}, {"name": "MeshFallbackPrepass", "path": "MeshFallbackPrepass"}, {"name": "Minecraft/Color", "path": "MinecraftColor"}, {"name": "Core/Screen/Mip", "path": "<PERSON><PERSON>"}, {"name": "Particle", "path": "Particle"}, {"name": "ParticleForwardPBR", "path": "ParticleForwardPBR"}, {"name": "ParticlePrepass", "path": "ParticlePrepass"}, {"name": "PopulateVolume", "path": "PopulateVolume"}, {"name": "PostFX", "path": "PostFX"}, {"name": "PostFX.Debug", "path": "PostFX.Debug"}, {"name": "PostFX.Debug.Texture2D", "path": "PostFX.Debug.Texture2D"}, {"name": "PostFX.Debug.Texture2DArray", "path": "PostFX.Debug.Texture2DArray"}, {"name": "PrepassFallback", "path": "PrepassFallback"}, {"name": "RTXPostFX", "path": "RTXPostFX"}, {"name": "RTXPostFX.Bloom", "path": "RTXPostFX.Bloom"}, {"name": "RTXPostFX.ToneMapping", "path": "RTXPostFX.Tonemapping"}, {"name": "RTXStub", "path": "RTXStub"}, {"name": "RenderChunk", "path": "RenderChunk"}, {"name": "RenderChunkForwardPBR", "path": "RenderChunkForwardPBR"}, {"name": "RenderChunkPrepass", "path": "RenderChunkPrepass"}, {"name": "Core/Screen/Blit", "path": "ScreenBlit"}, {"name": "ScreenSpaceReflections", "path": "ScreenSpaceReflections"}, {"name": "Debug/ScreenUV", "path": "ScreenUV"}, {"name": "ShadowOverlay", "path": "ShadowOverlay"}, {"name": "ShadowVolume", "path": "ShadowVolume"}, {"name": "SimpleOccluder", "path": "SimpleOccluder"}, {"name": "Sky", "path": "Sky"}, {"name": "SkyProbeDeferredShading", "path": "SkyProbeDeferredShading"}, {"name": "SpecularConvolution", "path": "SpecularConvolution"}, {"name": "StandardRed", "path": "StandardRed"}, {"name": "Stars", "path": "Stars"}, {"name": "StarsForwardPBR", "path": "StarsForwardPBR"}, {"name": "SunMoon", "path": "SunMoon"}, {"name": "SunMoonForwardPBR", "path": "SunMoonForwardPBR"}, {"name": "UI/Base", "path": "UIBase"}, {"name": "UI/Blit", "path": "UIBlit"}, {"name": "UI/Fill", "path": "UIFill"}, {"name": "UI/Glint", "path": "UIGlint"}, {"name": "UI/Sprite", "path": "UISprite"}, {"name": "UI/Text", "path": "UIText"}, {"name": "Upscaling", "path": "Upscaling"}, {"name": "VolumeScattering", "path": "VolumeScattering"}, {"name": "WaterExtinction", "path": "WaterExtinction"}, {"name": "WaterForwardPBR", "path": "WaterForwardPBR"}, {"name": "Weather", "path": "Weather"}, {"name": "WeatherForwardPBR", "path": "WeatherForwardPBR"}, {"name": "DisplayMap", "path": "displaymap"}]}
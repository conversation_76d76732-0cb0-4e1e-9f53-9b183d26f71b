@echo off
setlocal enabledelayedexpansion
title Restore Original Shaders
cls

echo ================================================================
echo                   Restore Original Shaders                     
echo ================================================================
echo.

:: Find latest backup
echo [*] Searching for backups...

if not exist "BACKUPS" (
    echo [X] BACKUPS folder not found!
    echo [*] Make sure BACKUPS folder exists
    pause
    exit /b 1
)

set "LATEST_BACKUP="
set "BACKUP_COUNT=0"

echo Available backups:
echo.

for /d %%d in ("BACKUPS\backup_*") do (
    set /a BACKUP_COUNT+=1
    echo !BACKUP_COUNT!. %%~nd
    set "BACKUP_!BACKUP_COUNT!=%%d"
    set "LATEST_BACKUP=%%d"
)

if !BACKUP_COUNT! equ 0 (
    echo [X] No backups found!
    pause
    exit /b 1
)

echo.
echo [?] Choose backup to restore:
echo [L] Use latest backup
echo [C] Cancel
echo.

choice /c LC123456789 /n >nul

if !errorlevel! equ 2 (
    echo [*] Cancelled
    pause
    exit /b 0
)

if !errorlevel! equ 1 (
    set "SELECTED_BACKUP=!LATEST_BACKUP!"
) else (
    set /a "BACKUP_NUM=!errorlevel!-2"
    if !BACKUP_NUM! leq !BACKUP_COUNT! (
        call set "SELECTED_BACKUP=%%BACKUP_!BACKUP_NUM!%%"
    ) else (
        echo [X] Invalid selection!
        pause
        exit /b 1
    )
)

echo [*] Selected backup: !SELECTED_BACKUP!
echo.

:: Find Minecraft app folder
echo [*] Searching for Minecraft...

set "MINECRAFT_APP_PATH="

for /d %%d in ("%ProgramFiles%\WindowsApps\Microsoft.MinecraftUWP*") do (
    if exist "%%d\data\renderer\materials" (
        set "MINECRAFT_APP_PATH=%%d"
        echo [+] Found Minecraft: %%d
        goto :minecraft_found
    )
)

for /d %%d in ("%ProgramFiles%\WindowsApps\Microsoft.MinecraftWindowsBeta*") do (
    if exist "%%d\data\renderer\materials" (
        set "MINECRAFT_APP_PATH=%%d"
        echo [+] Found Minecraft Preview: %%d
        goto :minecraft_found
    )
)

echo [X] Minecraft not found!
pause
exit /b 1

:minecraft_found
echo.

:: Restore materials
echo [*] Restoring original shaders...

if not exist "!SELECTED_BACKUP!\materials" (
    echo [X] Backup is corrupted or incomplete!
    pause
    exit /b 1
)

for %%f in ("!SELECTED_BACKUP!\materials\*.material.bin") do (
    echo [*] Restoring: %%~nf
    copy /Y "%%f" "!MINECRAFT_APP_PATH!\data\renderer\materials\" >nul 2>&1
    if !errorlevel! equ 0 (
        echo [+] Restored %%~nf successfully
    ) else (
        echo [!] Failed to restore %%~nf - trying with elevated permissions...
        
        :: Try with takeown and icacls
        takeown /f "!MINECRAFT_APP_PATH!\data\renderer\materials\%%~nxf" >nul 2>&1
        icacls "!MINECRAFT_APP_PATH!\data\renderer\materials\%%~nxf" /grant "%USERNAME%:F" >nul 2>&1
        copy /Y "%%f" "!MINECRAFT_APP_PATH!\data\renderer\materials\" >nul 2>&1
        
        if !errorlevel! equ 0 (
            echo [+] Restored %%~nf successfully (with elevated permissions)
        ) else (
            echo [X] Failed to restore %%~nf
        )
    )
)

echo.
echo ================================================================
echo                    RESTORATION COMPLETED                       
echo ================================================================
echo.
echo [+] Original shaders restored successfully!
echo [*] You can now run Minecraft with original shaders
echo.

pause

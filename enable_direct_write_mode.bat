@echo off
echo تفعيل Direct Write Mode لـ Matject...
echo.

:: إنشاء مجلد الإعدادات إذا لم يكن موجوداً
if not exist ".settings" mkdir .settings

:: تفعيل Direct Write Mode
echo being outside WindowsApps feels like freedom. [%date% // %time:~0,-6%]>".settings\directWriteMode.txt"

echo ✅ تم تفعيل Direct Write Mode بنجاح!
echo.
echo الآن يمكنك تشغيل Matject بدون الحاجة لـ IObit Unlocker
echo.
echo ملاحظة: هذا الوضع يعمل فقط إذا كان Minecraft مثبت خارج مجلد WindowsApps
echo أو إذا كنت تستخدم Bedrock Launcher
echo.
pause

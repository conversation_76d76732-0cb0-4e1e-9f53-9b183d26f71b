@echo off
setlocal enabledelayedexpansion
title Easy Shader Tool - اداة الشيدرات السهلة
color 0A

:: Check for Matject
if not exist "matject.bat" (
    echo Error: Matject not found
    echo خطأ: لم يتم العثور على Matject
    echo Please place this file in the same folder as matject.bat
    echo يجب وضع هذا الملف في نفس مجلد Matject
    pause
    exit
)

:: إعداد المتغيرات
set "MCPACKS_DIR=%cd%\MCPACKS"
set "MATERIALS_DIR=%cd%\MATERIALS"

:: إنشاء المجلدات إذا لم تكن موجودة
if not exist "%MCPACKS_DIR%" mkdir "%MCPACKS_DIR%"
if not exist "%MATERIALS_DIR%" mkdir "%MATERIALS_DIR%"
if not exist ".settings" mkdir ".settings"

:: تفعيل Direct Write Mode تلقائياً
echo تم التفعيل [%date% %time%] > ".settings\directWriteMode.txt"

:: إنشاء IObit Unlocker وهمي لتجاوز الفحص
if not exist "fake_iobit" mkdir fake_iobit
echo @echo off > "fake_iobit\IObitUnlocker.exe"
echo exit /b 0 >> "fake_iobit\IObitUnlocker.exe"
echo. > "fake_iobit\IObitUnlocker.dll"
echo %cd%\fake_iobit > ".settings\customIObitUnlockerPath.txt"

:: تعطيل الرسائل غير الضرورية
echo. > ".settings\disableConfirmation.txt"
echo. > ".settings\disableTips.txt"

:MAIN_MENU
cls
echo.
echo ================================================================
echo                    Easy Shader Tool
echo                   اداة الشيدرات السهلة
echo ================================================================
echo.
echo Shaders Folder: %MCPACKS_DIR%
echo مجلد الشيدرات: %MCPACKS_DIR%
echo.

:: فحص الشيدرات الموجودة
set "SHADER_COUNT=0"
set "SHADER_LIST="
for %%f in ("%MCPACKS_DIR%\*.mcpack" "%MCPACKS_DIR%\*.zip") do (
    set /a SHADER_COUNT+=1
    set "SHADER_LIST=!SHADER_LIST! %%~nxf"
)

if %SHADER_COUNT% equ 0 (
    echo 📦 الشيدرات الموجودة: لا توجد شيدرات
    echo.
    echo ┌─────────────────────────────────────────────────────────────┐
    echo │  🔽 [1] إضافة شيدر جديد                                      │
    echo │  ❌ [2] تطبيق شيدر (غير متاح - لا توجد شيدرات)              │
    echo │  🎮 [3] فتح Minecraft                                       │
    echo │  ⚙️  [4] إعدادات متقدمة                                      │
    echo │  🚪 [5] خروج                                                │
    echo └─────────────────────────────────────────────────────────────┘
) else (
    echo 📦 الشيدرات الموجودة (%SHADER_COUNT%):
    set "counter=0"
    for %%f in ("%MCPACKS_DIR%\*.mcpack" "%MCPACKS_DIR%\*.zip") do (
        set /a counter+=1
        echo    !counter!. %%~nxf
    )
    echo.
    echo ┌─────────────────────────────────────────────────────────────┐
    echo │  🔽 [1] إضافة شيدر جديد                                      │
    echo │  ✅ [2] تطبيق شيدر موجود                                     │
    echo │  🎮 [3] فتح Minecraft                                       │
    echo │  ⚙️  [4] إعدادات متقدمة                                      │
    echo │  🚪 [5] خروج                                                │
    echo └─────────────────────────────────────────────────────────────┘
)

echo.
set /p "choice=اختر رقم الخيار: "

if "%choice%"=="1" goto ADD_SHADER
if "%choice%"=="2" goto APPLY_SHADER
if "%choice%"=="3" goto OPEN_MINECRAFT
if "%choice%"=="4" goto ADVANCED_SETTINGS
if "%choice%"=="5" goto EXIT
goto MAIN_MENU

:ADD_SHADER
cls
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                        🔽 إضافة شيدر جديد                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 📁 سيتم فتح مجلد الشيدرات...
echo 📋 ضع ملف الشيدر (.mcpack أو .zip) في المجلد
echo.
start "" explorer "%MCPACKS_DIR%"
echo ⏳ بعد إضافة الشيدر، اضغط أي زر للمتابعة...
pause >nul
goto MAIN_MENU

:APPLY_SHADER
if %SHADER_COUNT% equ 0 (
    echo ❌ لا توجد شيدرات لتطبيقها
    pause
    goto MAIN_MENU
)

cls
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                        ✅ تطبيق الشيدر                         ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 📦 اختر الشيدر المطلوب تطبيقه:
echo.

set "counter=0"
for %%f in ("%MCPACKS_DIR%\*.mcpack" "%MCPACKS_DIR%\*.zip") do (
    set /a counter+=1
    echo [!counter!] %%~nxf
    set "shader_!counter!=%%f"
)

echo.
echo [0] العودة للقائمة الرئيسية
echo.
set /p "shader_choice=اختر رقم الشيدر: "

if "%shader_choice%"=="0" goto MAIN_MENU
if %shader_choice% gtr %SHADER_COUNT% goto APPLY_SHADER
if %shader_choice% lss 1 goto APPLY_SHADER

:: تطبيق الشيدر المختار
set "selected_shader=!shader_%shader_choice%!"
echo.
echo ⏳ جاري تطبيق الشيدر: !selected_shader!
echo.

:: تنظيف مجلد MATERIALS
if exist "%MATERIALS_DIR%\*.material.bin" del /q "%MATERIALS_DIR%\*.material.bin" >nul 2>&1

:: نسخ الشيدر المختار إلى مجلد مؤقت ومعالجته
if exist "temp_shader" rmdir /s /q "temp_shader" >nul 2>&1
mkdir "temp_shader"

:: استخراج الشيدر
echo 📦 استخراج الشيدر...
powershell -command "Expand-Archive -LiteralPath '!selected_shader!' -DestinationPath 'temp_shader' -Force" >nul 2>&1

:: البحث عن ملفات المواد
echo 🔍 البحث عن ملفات المواد...
if exist "temp_shader\renderer\materials\*.material.bin" (
    copy "temp_shader\renderer\materials\*.material.bin" "%MATERIALS_DIR%\" >nul 2>&1
    echo ✅ تم العثور على ملفات المواد في المجلد الرئيسي
) else (
    :: البحث في المجلدات الفرعية
    for /d %%d in ("temp_shader\*") do (
        if exist "%%d\renderer\materials\*.material.bin" (
            copy "%%d\renderer\materials\*.material.bin" "%MATERIALS_DIR%\" >nul 2>&1
            echo ✅ تم العثور على ملفات المواد في: %%~nd
            goto FOUND_MATERIALS
        )
    )
    
    :: البحث في subpacks
    for /d %%d in ("temp_shader\subpacks\*") do (
        if exist "%%d\renderer\materials\*.material.bin" (
            copy "%%d\renderer\materials\*.material.bin" "%MATERIALS_DIR%\" >nul 2>&1
            echo ✅ تم العثور على ملفات المواد في subpack: %%~nd
            goto FOUND_MATERIALS
        )
    )
    
    echo ❌ لم يتم العثور على ملفات المواد في الشيدر
    rmdir /s /q "temp_shader" >nul 2>&1
    pause
    goto MAIN_MENU
)

:FOUND_MATERIALS
:: تنظيف المجلد المؤقت
rmdir /s /q "temp_shader" >nul 2>&1

:: تشغيل Matject لتطبيق الشيدر
echo 🚀 تطبيق الشيدر على Minecraft...
echo.
call matject.bat placebo >nul 2>&1

echo.
echo ✅ تم تطبيق الشيدر بنجاح!
echo.
echo 🎮 هل تريد فتح Minecraft الآن؟
echo [Y] نعم    [N] لا
choice /c yn /n >nul
if errorlevel 2 goto MAIN_MENU
if errorlevel 1 goto OPEN_MINECRAFT

:OPEN_MINECRAFT
cls
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                        🎮 فتح Minecraft                      ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🚀 جاري فتح Minecraft...

:: محاولة فتح Minecraft
start "" shell:AppsFolder\Microsoft.MinecraftUWP_8wekyb3d8bbwe!App >nul 2>&1
if errorlevel 1 (
    echo ❌ فشل في فتح Minecraft تلقائياً
    echo 💡 يمكنك فتحه يدوياً من قائمة ابدأ
) else (
    echo ✅ تم فتح Minecraft بنجاح!
)

echo.
echo 📋 نصائح:
echo • تأكد من تفعيل الشيدر في إعدادات الموارد العامة
echo • إذا لم تظهر التأثيرات، أعد تشغيل اللعبة
echo • يمكنك تغيير الشيدر في أي وقت من هذه الأداة
echo.
pause
goto MAIN_MENU

:ADVANCED_SETTINGS
cls
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                        ⚙️ الإعدادات المتقدمة                    ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo [1] فتح Matject الأصلي
echo [2] إزالة جميع الشيدرات (استعادة افتراضي)
echo [3] فتح مجلد Minecraft
echo [4] تنظيف الملفات المؤقتة
echo [0] العودة للقائمة الرئيسية
echo.
set /p "adv_choice=اختر الخيار: "

if "%adv_choice%"=="1" (
    start "" "matject.bat"
    goto MAIN_MENU
)
if "%adv_choice%"=="2" goto RESTORE_DEFAULT
if "%adv_choice%"=="3" (
    start "" explorer "shell:AppsFolder"
    goto ADVANCED_SETTINGS
)
if "%adv_choice%"=="4" goto CLEANUP
if "%adv_choice%"=="0" goto MAIN_MENU
goto ADVANCED_SETTINGS

:RESTORE_DEFAULT
echo.
echo ⚠️ هذا سيزيل جميع الشيدرات ويستعيد المواد الافتراضية
echo هل أنت متأكد؟ [Y/N]
choice /c yn /n >nul
if errorlevel 2 goto ADVANCED_SETTINGS

echo 🔄 جاري استعادة المواد الافتراضية...
call matject.bat placebo >nul 2>&1
echo ✅ تم استعادة المواد الافتراضية
pause
goto MAIN_MENU

:CLEANUP
echo 🧹 تنظيف الملفات المؤقتة...
if exist "temp_shader" rmdir /s /q "temp_shader" >nul 2>&1
if exist "tmp" rmdir /s /q "tmp" >nul 2>&1
del "%MATERIALS_DIR%\*.material.bin" >nul 2>&1
echo ✅ تم تنظيف الملفات المؤقتة
pause
goto ADVANCED_SETTINGS

:EXIT
cls
echo.
echo 👋 شكراً لاستخدام أداة الشيدرات السهلة!
echo 🎮 استمتع بـ Minecraft مع الشيدرات الرائعة!
echo.
pause
exit

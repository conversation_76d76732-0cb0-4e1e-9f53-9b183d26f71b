@echo off
echo استعادة إعدادات Matject الأصلية...
echo.

:: استعادة الملف الأصلي إذا كان موجوداً
if exist "matject_backup.bat" (
    copy "matject_backup.bat" "matject.bat" >nul
    echo ✅ تم استعادة matject.bat الأصلي
) else (
    echo ⚠️ لم يتم العثور على النسخة الاحتياطية
)

:: حذف ملفات Direct Write Mode
if exist ".settings\directWriteMode.txt" (
    del ".settings\directWriteMode.txt" >nul
    echo ✅ تم إلغاء تفعيل Direct Write Mode
)

:: حذف مسار IObit Unlocker المخصص
if exist ".settings\customIObitUnlockerPath.txt" (
    del ".settings\customIObitUnlockerPath.txt" >nul
    echo ✅ تم حذف مسار IObit Unlocker المخصص
)

:: حذف مجلد IObit Unlocker الوهمي
if exist "fake_iobit" (
    rmdir /s /q "fake_iobit" >nul
    echo ✅ تم حذف IObit Unlocker الوهمي
)

echo.
echo ✅ تم استعادة جميع الإعدادات الأصلية
echo الآن Matject سيطلب تثبيت IObit Unlocker مرة أخرى
echo.
pause

@echo off
title Easy Shader Tool

if not exist "matject.bat" (
    echo ERROR: Put this file with matject.bat
    pause
    exit
)

if not exist "MCPACKS" mkdir MCPACKS
if not exist ".settings" mkdir .settings
echo Enabled > .settings\directWriteMode.txt

if not exist "fake_iobit" mkdir fake_iobit
echo @echo off > fake_iobit\IObitUnlocker.exe
echo %cd%\fake_iobit > .settings\customIObitUnlockerPath.txt

:MENU
cls
echo.
echo Easy Shader Tool
echo ================
echo.

set /a count=0
for %%f in ("MCPACKS\*.mcpack" "MCPACKS\*.zip") do set /a count+=1

echo Shaders: %count%
echo.
echo [1] Add Shader
if %count% gtr 0 echo [2] Apply Shader
echo [3] Open Minecraft
echo [4] Exit
echo.

if %count% gtr 0 (
    choice /c 1234 /n
    if errorlevel 4 exit
    if errorlevel 3 goto GAME
    if errorlevel 2 goto USE
    if errorlevel 1 goto ADD
) else (
    choice /c 134 /n
    if errorlevel 3 exit
    if errorlevel 2 goto GAME
    if errorlevel 1 goto ADD
)

:ADD
echo.
echo Put shader file in MCPACKS folder
start explorer MCPA<PERSON><PERSON>
pause
goto MENU

:USE
cls
echo.
echo Choose Shader:
echo.

set /a n=0
for %%f in ("MCPACKS\*.mcpack" "MCPACKS\*.zip") do (
    set /a n+=1
    echo [!n!] %%~nxf
    set "s!n!=%%f"
)

echo.
set /p c="Number: "

if %c% gtr %n% goto USE
if %c% lss 1 goto USE

set "shader=!s%c%!"
echo.
echo Applying shader...

if not exist "MATERIALS" mkdir MATERIALS
del MATERIALS\*.material.bin 2>nul

if exist temp rmdir /s /q temp
mkdir temp

powershell "Expand-Archive '!shader!' temp" 2>nul

if exist "temp\renderer\materials\*.material.bin" (
    copy temp\renderer\materials\*.material.bin MATERIALS\ >nul
) else (
    for /d %%d in (temp\*) do (
        if exist "%%d\renderer\materials\*.material.bin" copy "%%d\renderer\materials\*.material.bin" MATERIALS\ >nul
    )
)

rmdir /s /q temp

call matject.bat >nul 2>&1

echo Done! Open Minecraft?
choice /c yn /n
if errorlevel 1 goto GAME
goto MENU

:GAME
start shell:AppsFolder\Microsoft.MinecraftUWP_8wekyb3d8bbwe!App
echo Minecraft opened. Enable shader in settings.
pause
goto MENU

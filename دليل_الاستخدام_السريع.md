# دليل الاستخدام السريع - أدوات الشيدرات البسيطة

## 🎯 **الملفات المنشأة**

تم إنشاء 3 أدوات مختلفة حسب مستوى الخبرة:

### 1. **`تشغيل_الشيدرات.bat`** ⭐ (الأبسط - للمبتدئين)
- واجهة عربية بسيطة جداً
- خطوات واضحة ومفصلة
- مناسب للمستخدمين الجدد

### 2. **`SimpleShader.bat`** (متوسط - للاستخدام اليومي)
- واجهة مختلطة (عربي/إنجليزي)
- خيارات أكثر وسرعة أعلى
- مناسب للاستخدام المتكرر

### 3. **`EasyShaderTool.bat`** (متقدم - للخبراء)
- واجهة شاملة مع خيارات متقدمة
- إعدادات إضافية وأدوات مساعدة
- مناسب للمستخدمين المتقدمين

## 🚀 **البدء السريع**

### الخطوة 1: الإعداد
```
1. ضع أي من الملفات الثلاثة في مجلد Matject
2. شغل الملف المناسب لك
3. ستتم جميع الإعدادات تلقائياً
```

### الخطوة 2: إضافة شيدر
```
1. اختر "إضافة شيدر"
2. ضع ملف الشيدر (.mcpack أو .zip) في المجلد المفتوح
3. اضغط أي زر للعودة
```

### الخطوة 3: تطبيق الشيدر
```
1. اختر "تطبيق شيدر"
2. اختر الشيدر المطلوب من القائمة
3. انتظر حتى اكتمال التطبيق
```

### الخطوة 4: فتح Minecraft
```
1. اختر "فتح Minecraft"
2. ادخل إعدادات اللعبة
3. فعل الشيدر من "الموارد العامة"
```

## ✨ **المميزات الرئيسية**

### 🛡️ **أمان كامل**
- ✅ لا حاجة لـ IObit Unlocker
- ✅ يعمل بـ Direct Write Mode
- ✅ نسخ احتياطية تلقائية
- ✅ لا يتصل بالإنترنت

### 🎮 **سهولة الاستخدام**
- ✅ واجهة عربية واضحة
- ✅ خطوات مفصلة
- ✅ إعداد تلقائي
- ✅ فتح Minecraft مباشر

### ⚡ **سرعة وكفاءة**
- ✅ تطبيق سريع للشيدرات
- ✅ استخراج تلقائي للملفات
- ✅ تنظيف تلقائي للملفات المؤقتة
- ✅ دعم جميع أنواع الشيدرات

## 🔧 **الإعدادات التلقائية**

عند تشغيل أي من الأدوات لأول مرة، سيتم:

```
✅ تفعيل Direct Write Mode
✅ إنشاء IObit Unlocker وهمي
✅ تعطيل الرسائل غير الضرورية
✅ إنشاء المجلدات المطلوبة
✅ تعطيل فحص التحديثات
```

## 📁 **هيكل المجلدات**

```
📁 مجلد Matject/
├── 📄 تشغيل_الشيدرات.bat (الأداة البسيطة)
├── 📄 SimpleShader.bat (الأداة المتوسطة)
├── 📄 EasyShaderTool.bat (الأداة المتقدمة)
├── 📄 matject.bat (الأداة الأصلية)
├── 📁 MCPACKS/ (ضع الشيدرات هنا)
├── 📁 MATERIALS/ (ملفات مؤقتة)
├── 📁 .settings/ (إعدادات تلقائية)
└── 📁 fake_iobit/ (IObit وهمي)
```

## ⚠️ **نصائح مهمة**

### قبل الاستخدام:
- 🎮 **أغلق Minecraft** تماماً
- 👨‍💼 **شغل كمدير** (Run as Administrator)
- 💾 **انشئ نقطة استعادة** للنظام

### أثناء الاستخدام:
- ⏳ **لا تلغي العملية** في المنتصف
- 📂 **انتظر** حتى اكتمال التطبيق
- 🔄 **لا تشغل عدة أدوات** في نفس الوقت

### بعد الاستخدام:
- 🎮 **فعل الشيدر** في إعدادات اللعبة
- 🔄 **أعد تشغيل العالم** لرؤية التأثيرات
- 💾 **احتفظ بنسخة احتياطية** من الشيدرات المفضلة

## 🆘 **حل المشاكل**

### إذا لم يعمل الشيدر:
```
1. تأكد من أن الشيدر مخصص لـ Minecraft Bedrock
2. تأكد من تفعيله في إعدادات الموارد العامة
3. أعد تشغيل Minecraft
4. جرب شيدر آخر للتأكد
```

### إذا ظهرت رسائل خطأ:
```
1. شغل الأداة كمدير
2. تأكد من إغلاق Minecraft
3. أعد تشغيل الكمبيوتر
4. جرب الأداة البسيطة بدلاً من المتقدمة
```

### إذا لم تظهر التأثيرات:
```
1. تأكد من دعم جهازك للشيدرات
2. جرب شيدر أبسط
3. تحقق من إعدادات الرسوميات في اللعبة
4. أعد تطبيق الشيدر
```

## 🎯 **التوصيات**

### للمبتدئين:
- استخدم **`تشغيل_الشيدرات.bat`**
- ابدأ بشيدرات بسيطة
- اقرأ التعليمات بعناية

### للاستخدام اليومي:
- استخدم **`SimpleShader.bat`**
- احتفظ بالشيدرات المفضلة
- جرب شيدرات مختلفة

### للمتقدمين:
- استخدم **`EasyShaderTool.bat`**
- استكشف الإعدادات المتقدمة
- ساعد المبتدئين

## 🎮 **استمتع بـ Minecraft مع الشيدرات الرائعة!**

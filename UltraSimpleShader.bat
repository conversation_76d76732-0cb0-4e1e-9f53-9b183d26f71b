title Ultra Simple Shader Tool
@echo off

net session >nul 2>&1 || (echo Run as Administrator! & pause & exit)

for /f "tokens=*" %%i in ('powershell "(Get-AppxPackage -Name Microsoft.MinecraftUWP).InstallLocation"') do set MC=%%i
if "%MC%"=="" (echo Minecraft not found! & pause & exit)

if not exist SHADERS mkdir SHADERS
if not exist BACKUP mkdir BACKUP

:MENU
cls
echo Ultra Simple Shader Tool
echo ========================
echo.

set /a c=0
for %%f in (SHADERS\*.mcpack SHADERS\*.zip) do set /a c+=1

echo Shaders: %c%
echo.
echo [1] Add Shader
if %c% gtr 0 echo [2] Apply Shader
echo [3] Remove Shader  
echo [4] Open Minecraft
echo [5] Exit
echo.

if %c% gtr 0 (
    choice /c 12345 /n
    if errorlevel 5 exit
    if errorlevel 4 start shell:AppsFolder\Microsoft.MinecraftUWP_8wekyb3d8bbwe!App & goto MENU
    if errorlevel 3 goto REMOVE
    if errorlevel 2 goto APPLY
    if errorlevel 1 goto ADD
) else (
    choice /c 1345 /n
    if errorlevel 4 exit
    if errorlevel 3 start shell:AppsFolder\Microsoft.MinecraftUWP_8wekyb3d8bbwe!App & goto MENU
    if errorlevel 2 goto REMOVE
    if errorlevel 1 goto ADD
)

:ADD
start explorer SHADERS
echo Put shader files in SHADERS folder, then press any key
pause >nul
goto MENU

:APPLY
cls
echo Choose Shader:
echo.

if not exist BACKUP\*.material.bin copy "%MC%\data\renderer\materials\*.material.bin" BACKUP\ >nul

set /a n=0
for %%f in (SHADERS\*.mcpack SHADERS\*.zip) do (
    set /a n+=1
    echo [!n!] %%~nxf
    set s!n!=%%f
)

set /p choice="Number: "
if %choice% gtr %n% goto APPLY
if %choice% lss 1 goto APPLY

setlocal enabledelayedexpansion
set shader=!s%choice%!

if exist temp rmdir /s /q temp
mkdir temp
powershell "Expand-Archive '!shader!' temp" >nul

if exist temp\renderer\materials\*.material.bin (
    copy temp\renderer\materials\*.material.bin "%MC%\data\renderer\materials\" >nul
) else (
    for /d %%d in (temp\*) do if exist %%d\renderer\materials\*.material.bin copy %%d\renderer\materials\*.material.bin "%MC%\data\renderer\materials\" >nul
)

rmdir /s /q temp
echo Shader applied! Enable it in Minecraft settings.
pause
goto MENU

:REMOVE
if exist BACKUP\*.material.bin (
    copy BACKUP\*.material.bin "%MC%\data\renderer\materials\" >nul
    echo Shader removed!
) else (
    echo No backup found!
)
pause
goto MENU

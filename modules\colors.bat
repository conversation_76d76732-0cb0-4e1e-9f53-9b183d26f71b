:: colors.bat // Made by github.com/faizul726
@echo off

set "GRY=[90m"
set "RED=[91m"
set "GRN=[92m"
set "YLW=[93m"
set "BLU=[94m"
set "CYN=[96m"
set "WHT=[97m"
set "RST=[0m"
set "ERR=[41;97m"

set "p1=[107m  [0m"

set "hideCursor=[?25l"
set "showCursor=[?25h"
:: set "BLINK=[5m"

set "BEL="

if defined murgi goto:EOF

title too late :doggysmurk:

echo Oh yes, %RED%C%GRN%O%BLU%L%YLW%O%CYN%R%RED%S%RST%...
echo.
timeout 1 >nul
echo %p1:107=42%%p1:107=42%%p1:107=42%%p1:107=42%%p1:107=42%%p1:107=42%%p1:107=42%%p1:107=42%%p1:107=42%%p1:107=42%
timeout 1 >nul
echo %p1:107=42%%p1:107=42%%p1:107=42%%p1:107=42%%p1:107=42%%p1:107=42%%p1:107=42%%p1:107=42%%p1:107=42%%p1:107=42%
timeout 1 >nul
echo %p1:107=42%%p1:107=42%%p1:107=0%%p1:107=0%%p1:107=42%%p1:107=42%%p1:107=0%%p1:107=0%%p1:107=42%%p1:107=42%
timeout 1 >nul
echo %p1:107=42%%p1:107=42%%p1:107=0%%p1:107=0%%p1:107=42%%p1:107=42%%p1:107=0%%p1:107=0%%p1:107=42%%p1:107=42%
timeout 1 >nul
echo %p1:107=42%%p1:107=42%%p1:107=42%%p1:107=42%%p1:107=0%%p1:107=0%%p1:107=42%%p1:107=42%%p1:107=42%%p1:107=42%
timeout 1 >nul
echo %p1:107=42%%p1:107=42%%p1:107=42%%p1:107=0%%p1:107=0%%p1:107=0%%p1:107=0%%p1:107=42%%p1:107=42%%p1:107=42%
timeout 1 >nul
echo %p1:107=42%%p1:107=42%%p1:107=42%%p1:107=0%%p1:107=0%%p1:107=0%%p1:107=0%%p1:107=42%%p1:107=42%%p1:107=42%
timeout 1 >nul
echo %p1:107=42%%p1:107=42%%p1:107=42%%p1:107=0%%p1:107=42%%p1:107=42%%p1:107=0%%p1:107=42%%p1:107=42%%p1:107=42%
timeout 1 >nul
echo %p1:107=42%%p1:107=42%%p1:107=42%%p1:107=42%%p1:107=42%%p1:107=42%%p1:107=42%%p1:107=42%%p1:107=42%%p1:107=42%
timeout 1 >nul
echo %p1:107=42%%p1:107=42%%p1:107=42%%p1:107=42%%p1:107=42%%p1:107=42%%p1:107=42%%p1:107=42%%p1:107=42%%p1:107=42%
timeout 1 >nul
echo.
echo Realm of gathering woes?
echo.
endlocal
echo on
@cmd /k
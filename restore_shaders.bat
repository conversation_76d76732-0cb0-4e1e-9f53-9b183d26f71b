@echo off
setlocal enabledelayedexpansion
title استعادة الشيدرات الأصلية
cls

:: Colors
set "RED=[91m"
set "GRN=[92m"
set "YLW=[93m"
set "BLU=[94m"
set "CYN=[96m"
set "WHT=[97m"
set "RST=[0m"

echo !CYN!╔══════════════════════════════════════════════════════════════╗!RST!
echo !CYN!║                   !WHT!استعادة الشيدرات الأصلية!CYN!                  ║!RST!
echo !CYN!╚══════════════════════════════════════════════════════════════╝!RST!
echo.

:: Find latest backup
echo !YLW![*] البحث عن النسخ الاحتياطية...!RST!

if not exist "BACKUPS" (
    echo !RED![✗] لم يتم العثور على مجلد النسخ الاحتياطية!RST!
    echo !YLW![*] تأكد من وجود مجلد BACKUPS!RST!
    pause
    exit /b 1
)

set "LATEST_BACKUP="
set "BACKUP_COUNT=0"

echo !GRN!النسخ الاحتياطية المتاحة:!RST!
echo.

for /d %%d in ("BACKUPS\backup_*") do (
    set /a BACKUP_COUNT+=1
    echo !BACKUP_COUNT!. %%~nd
    set "BACKUP_!BACKUP_COUNT!=%%d"
    set "LATEST_BACKUP=%%d"
)

if !BACKUP_COUNT! equ 0 (
    echo !RED![✗] لم يتم العثور على نسخ احتياطية!RST!
    pause
    exit /b 1
)

echo.
echo !YLW![?] اختر النسخة الاحتياطية للاستعادة:!RST!
echo !GRN![L] استخدام آخر نسخة احتياطية!RST!
echo !RED![C] إلغاء!RST!
echo.

choice /c LC123456789 /n >nul

if !errorlevel! equ 2 (
    echo !YLW![*] تم الإلغاء!RST!
    pause
    exit /b 0
)

if !errorlevel! equ 1 (
    set "SELECTED_BACKUP=!LATEST_BACKUP!"
) else (
    set /a "BACKUP_NUM=!errorlevel!-2"
    if !BACKUP_NUM! leq !BACKUP_COUNT! (
        call set "SELECTED_BACKUP=%%BACKUP_!BACKUP_NUM!%%"
    ) else (
        echo !RED![✗] اختيار غير صحيح!RST!
        pause
        exit /b 1
    )
)

echo !GRN![*] النسخة المختارة: !SELECTED_BACKUP!!RST!
echo.

:: Find Minecraft app folder
echo !YLW![*] البحث عن Minecraft...!RST!

set "MINECRAFT_APP_PATH="

for /d %%d in ("%ProgramFiles%\WindowsApps\Microsoft.MinecraftUWP*") do (
    if exist "%%d\data\renderer\materials" (
        set "MINECRAFT_APP_PATH=%%d"
        echo !GRN![✓] تم العثور على Minecraft: %%d!RST!
        goto :minecraft_found
    )
)

for /d %%d in ("%ProgramFiles%\WindowsApps\Microsoft.MinecraftWindowsBeta*") do (
    if exist "%%d\data\renderer\materials" (
        set "MINECRAFT_APP_PATH=%%d"
        echo !GRN![✓] تم العثور على Minecraft Preview: %%d!RST!
        goto :minecraft_found
    )
)

echo !RED![✗] لم يتم العثور على Minecraft!RST!
pause
exit /b 1

:minecraft_found
echo.

:: Restore materials
echo !YLW![*] استعادة الشيدرات الأصلية...!RST!

if not exist "!SELECTED_BACKUP!\materials" (
    echo !RED![✗] النسخة الاحتياطية تالفة أو غير مكتملة!RST!
    pause
    exit /b 1
)

for %%f in ("!SELECTED_BACKUP!\materials\*.material.bin") do (
    echo !GRN![*] استعادة: %%~nf!RST!
    copy /Y "%%f" "!MINECRAFT_APP_PATH!\data\renderer\materials\" >nul 2>&1
    if !errorlevel! equ 0 (
        echo !GRN![✓] تم استعادة %%~nf بنجاح!RST!
    ) else (
        echo !YLW![!] فشل في استعادة %%~nf - جاري المحاولة بصلاحيات أعلى...!RST!
        
        :: Try with takeown and icacls
        takeown /f "!MINECRAFT_APP_PATH!\data\renderer\materials\%%~nxf" >nul 2>&1
        icacls "!MINECRAFT_APP_PATH!\data\renderer\materials\%%~nxf" /grant "%USERNAME%:F" >nul 2>&1
        copy /Y "%%f" "!MINECRAFT_APP_PATH!\data\renderer\materials\" >nul 2>&1
        
        if !errorlevel! equ 0 (
            echo !GRN![✓] تم استعادة %%~nf بنجاح ^(بصلاحيات أعلى^)!RST!
        ) else (
            echo !RED![✗] فشل في استعادة %%~nf!RST!
        )
    )
)

echo.
echo !GRN!╔══════════════════════════════════════════════════════════════╗!RST!
echo !GRN!║                    !WHT!تم الانتهاء من الاستعادة!GRN!                 ║!RST!
echo !GRN!╚══════════════════════════════════════════════════════════════╝!RST!
echo.
echo !GRN![✓] تم استعادة الشيدرات الأصلية بنجاح!!RST!
echo !YLW![*] يمكنك الآن تشغيل Minecraft مع الشيدرات الأصلية!RST!
echo.

pause

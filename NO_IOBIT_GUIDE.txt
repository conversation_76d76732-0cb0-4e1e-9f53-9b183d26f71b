MINECRAFT SHADER TOOLS - NO IOBIT UNLOCKER REQUIRED
===================================================

IMPORTANT: These tools work completely independently and do NOT require IObit Unlocker!

NEW TOOLS CREATED:
------------------
1. UltraSimpleShader.bat    - Simplest possible (recommended for beginners)
2. SimpleShaderApplier.bat  - Clean and easy to use
3. IndependentShaderTool.bat - Full featured with backup system
4. RemoveIObitDependency.bat - Removes IObit from original Matject

HOW THEY WORK:
--------------
- Direct file operations (no IObit needed)
- Administrator privileges for file access
- Automatic Minecraft detection
- Built-in backup system
- No internet connection required

QUICK START:
------------
1. Choose any tool above
2. Right-click > "Run as administrator"
3. Add your shader files
4. Apply shader
5. Open Minecraft and enable shader

DETAILED INSTRUCTIONS:
----------------------

STEP 1: Choose Your Tool
- Beginners: Use UltraSimpleShader.bat
- Regular users: Use SimpleShaderApplier.bat  
- Advanced users: Use IndependentShaderTool.bat

STEP 2: Run as Administrator
- Right-click the .bat file
- Select "Run as administrator"
- Click "Yes" when prompted

STEP 3: Add Shaders
- Choose "Add Shader" option
- Put your .mcpack or .zip files in the opened folder
- Press any key to continue

STEP 4: Apply Shader
- Choose "Apply Shader" option
- Select which shader to use
- Wait for "SUCCESS" message

STEP 5: Open Minecraft
- Choose "Open Minecraft" option
- Go to Settings > Global Resource Packs
- Enable your shader pack
- Apply and restart world

FEATURES:
---------
✓ No IObit Unlocker required
✓ Works with all Windows versions
✓ Automatic backup creation
✓ Direct Minecraft integration
✓ Support for .mcpack and .zip files
✓ Easy shader switching
✓ One-click Minecraft launch
✓ Safe and reversible

REQUIREMENTS:
-------------
- Windows 10/11
- Minecraft Bedrock Edition (from Microsoft Store)
- Administrator privileges
- Shader files (.mcpack or .zip format)

FOLDER STRUCTURE:
-----------------
Your Folder/
├── UltraSimpleShader.bat (or other tool)
├── SHADERS/ (put shader files here)
├── BACKUP/ (automatic backups)
└── temp/ (temporary files - auto-cleaned)

TROUBLESHOOTING:
----------------

Problem: "Run as Administrator required"
Solution: Right-click the .bat file and select "Run as administrator"

Problem: "Minecraft not found"
Solution: Install Minecraft Bedrock from Microsoft Store

Problem: Shader doesn't work
Solution: Make sure it's a Bedrock Edition shader, not Java Edition

Problem: No effects visible
Solution: Enable the shader in Minecraft's Global Resource Packs settings

Problem: Game crashes
Solution: Try a different shader or restore default materials

SAFETY NOTES:
-------------
- All tools create automatic backups
- Original materials are preserved
- Easy to restore defaults
- No system files are modified
- No permanent changes to Windows

COMPARISON WITH ORIGINAL MATJECT:
---------------------------------
Original Matject:
- Requires IObit Unlocker installation
- Complex setup process
- Many dependencies
- Advanced features

These New Tools:
- No IObit Unlocker needed
- Simple setup (just run)
- Self-contained
- Focus on core functionality

WHICH TOOL TO USE:
------------------

UltraSimpleShader.bat:
- Absolute simplest
- Minimal interface
- Perfect for beginners
- Just the essentials

SimpleShaderApplier.bat:
- Clean interface
- Good balance of features
- Suitable for regular use
- Clear error messages

IndependentShaderTool.bat:
- Most comprehensive
- Advanced backup system
- Detailed status messages
- Best for power users

TIPS:
-----
- Always close Minecraft before applying shaders
- Keep your favorite shaders in the SHADERS folder
- Try different shaders to find your preference
- Some shaders may affect game performance
- You can switch between shaders anytime

ENJOY YOUR SHADERS WITHOUT IOBIT UNLOCKER!
